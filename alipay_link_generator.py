#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爱奇艺支付宝链接生成器
分析并生成 alipays://platformapi/startapp 格式的支付链接
"""

import hashlib
import time
import random
import string
from urllib.parse import quote, urlencode

class IqiyiAlipayLinkGenerator:
    def __init__(self):
        # 固定参数
        self.app_id = "20000067"  # 支付宝小程序ID
        self.base_url = "https://vip.iqiyi.com/html5VIP/activity/quickPayWrap/index.html"
        
        # SKU配置
        self.sku_configs = {
            'vip_month': {
                'sku_id': 'sku_555029368976707642',
                'price': 19.8,
                'name': '爱奇艺VIP月卡'
            },
            'vip_year': {
                'sku_id': 'sku_555029368976707643', 
                'price': 198,
                'name': '爱奇艺VIP年卡'
            },
            'sports_month': {
                'sku_id': 'sku_555029368976707644',
                'price': 39.8,
                'name': '爱奇艺体育月卡'
            }
        }

    def generate_order_no(self):
        """生成订单号"""
        # 格式: YYYYMMDDHHMMSS + 6位随机数
        timestamp = time.strftime("%Y%m%d%H%M%S")
        random_suffix = ''.join(random.choices(string.digits, k=6))
        return timestamp + random_suffix

    def generate_fv(self):
        """生成fv参数 (可能是指纹或版本号)"""
        # 基于时间戳生成16位十六进制字符串
        timestamp = str(int(time.time() * 1000))
        hash_obj = hashlib.md5(timestamp.encode())
        return hash_obj.hexdigest()[:16]

    def generate_sign(self, order_no, sku_id, fv):
        """生成签名"""
        # 模拟签名算法 (实际算法可能更复杂)
        sign_data = f"{order_no}{sku_id}{fv}iqiyi_secret_key"
        return hashlib.md5(sign_data.encode()).hexdigest()

    def build_iqiyi_url(self, product_type, order_no=None):
        """构建爱奇艺支付页面URL"""
        if product_type not in self.sku_configs:
            raise ValueError(f"不支持的产品类型: {product_type}")
        
        config = self.sku_configs[product_type]
        
        # 生成参数
        if not order_no:
            order_no = self.generate_order_no()
        
        fv = self.generate_fv()
        sku_id = config['sku_id']
        sign = self.generate_sign(order_no, sku_id, fv)
        
        # 构建URL参数
        params = {
            'fv': fv,
            'orderNo': order_no,
            'payMethod': 'alipay',
            'sign': sign,
            'skuId': sku_id
        }
        
        # 构建完整URL
        iqiyi_url = f"{self.base_url}?{urlencode(params)}"
        
        return iqiyi_url, {
            'order_no': order_no,
            'fv': fv,
            'sign': sign,
            'sku_id': sku_id,
            'product_name': config['name'],
            'price': config['price']
        }

    def generate_alipay_link(self, product_type, order_no=None):
        """生成完整的支付宝链接"""
        # 构建爱奇艺URL
        iqiyi_url, order_info = self.build_iqiyi_url(product_type, order_no)
        
        # URL编码
        encoded_url = quote(iqiyi_url, safe='')
        
        # 构建支付宝链接
        alipay_params = {
            'appId': self.app_id,
            'url': encoded_url
        }
        
        alipay_link = f"alipays://platformapi/startapp?{urlencode(alipay_params)}"
        
        return {
            'alipay_link': alipay_link,
            'iqiyi_url': iqiyi_url,
            'order_info': order_info
        }

    def batch_generate(self, product_type, count=1):
        """批量生成链接"""
        results = []
        
        for i in range(count):
            try:
                result = self.generate_alipay_link(product_type)
                results.append(result)
            except Exception as e:
                print(f"生成第{i+1}个链接失败: {e}")
        
        return results

def main():
    """主函数"""
    generator = IqiyiAlipayLinkGenerator()
    
    print("🔗 爱奇艺支付宝链接生成器")
    print("=" * 60)
    
    # 显示可用产品
    print("可用产品类型:")
    for key, config in generator.sku_configs.items():
        print(f"  {key}: {config['name']} - ¥{config['price']}")
    
    print("\n" + "=" * 60)
    
    # 用户选择
    while True:
        try:
            product_type = input("\n请输入产品类型 (vip_month/vip_year/sports_month): ").strip()
            
            if product_type not in generator.sku_configs:
                print("❌ 无效的产品类型")
                continue
            
            # 询问是否自定义订单号
            custom_order = input("是否自定义订单号? (y/n): ").strip().lower()
            order_no = None
            
            if custom_order == 'y':
                order_no = input("请输入订单号: ").strip()
                if not order_no:
                    order_no = None
            
            # 生成链接
            result = generator.generate_alipay_link(product_type, order_no)
            
            print("\n" + "=" * 60)
            print("✅ 链接生成成功!")
            print("=" * 60)
            
            print(f"📱 支付宝链接:")
            print(result['alipay_link'])
            
            print(f"\n📺 爱奇艺URL:")
            print(result['iqiyi_url'])
            
            print(f"\n📋 订单信息:")
            order_info = result['order_info']
            for key, value in order_info.items():
                print(f"  {key}: {value}")
            
            # 保存到文件
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"alipay_link_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"产品类型: {product_type}\n")
                f.write(f"产品名称: {order_info['product_name']}\n")
                f.write(f"价格: ¥{order_info['price']}\n")
                f.write(f"订单号: {order_info['order_no']}\n")
                f.write(f"签名: {order_info['sign']}\n")
                f.write(f"\n支付宝链接:\n{result['alipay_link']}\n")
                f.write(f"\n爱奇艺URL:\n{result['iqiyi_url']}\n")
            
            print(f"\n💾 链接已保存到: {filename}")
            
            # 询问是否继续
            continue_gen = input("\n是否继续生成? (y/n): ").strip().lower()
            if continue_gen != 'y':
                break
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()
