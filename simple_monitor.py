#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版支付流程监控器
直接请求API接口，分析支付流程
"""

import json
import time
import requests
from datetime import datetime
from urllib.parse import urlparse, parse_qs

class SimplePaymentMonitor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh-Hans;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty'
        })
        
        self.api_base = 'https://api.zzqz2024.com'
        self.h5_base = 'https://h5.syhy123.com'
        
        # 从HAR文件中提取的关键参数
        self.channel_code = '0ba8c5e5f1d60cf6658f6a266a6feb2b'
        self.project_id = '7526720476899622948'
        self.promotion_id = '7526720211249496127'
        
        self.logs = []

    def log(self, level, message, data=None):
        """记录日志"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'level': level,
            'message': message,
            'data': data
        }
        self.logs.append(log_entry)
        
        print(f"[{timestamp}] {level.upper()}: {message}")
        if data and isinstance(data, dict):
            print(f"  数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

    def test_h5_page(self, phone='13802913949'):
        """测试H5页面访问"""
        self.log('INFO', '🌐 测试H5页面访问')
        
        url = f"{self.h5_base}/aqy_msv2/"
        params = {
            'a': self.channel_code,
            'projectid': self.project_id,
            'promotionid': self.promotion_id,
            'creativetype': '5',
            'phone': phone,
            'product': 'vip_month'
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            self.log('SUCCESS', f'H5页面访问成功: {response.status_code}', {
                'url': response.url,
                'status_code': response.status_code,
                'content_length': len(response.text)
            })
            
            # 分析页面内容，查找API调用
            self.analyze_page_content(response.text)
            
            return response
            
        except Exception as e:
            self.log('ERROR', f'H5页面访问失败: {e}')
            return None

    def analyze_page_content(self, content):
        """分析页面内容，查找API端点"""
        self.log('INFO', '🔍 分析页面内容')
        
        # 查找API URL
        api_patterns = [
            'api.zzqz2024.com',
            'signOrder',
            'createOrder',
            'payment',
            'alipay'
        ]
        
        found_apis = []
        for pattern in api_patterns:
            if pattern in content:
                found_apis.append(pattern)
        
        if found_apis:
            self.log('SUCCESS', f'发现API模式: {found_apis}')
        else:
            self.log('WARNING', '未发现明显的API模式')

    def test_sign_order_api(self, phone='13802913949'):
        """测试签名订单API"""
        self.log('INFO', '💰 测试签名订单API')
        
        url = f"{self.api_base}/api/am/command/encrypt/signOrder"
        
        # 构建请求数据（基于HAR文件中的格式）
        data = {
            'channelCode': self.channel_code,
            'data': self.generate_encrypted_data(phone),
            'iv': str(int(time.time() * 1000)),
            't': self.generate_signature(),
            'uuid': self.generate_uuid(),
            'traceId': self.generate_uuid()
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Origin': self.h5_base,
            'Referer': f'{self.h5_base}/',
        }
        
        try:
            response = self.session.post(url, json=data, headers=headers, timeout=10)
            
            self.log('INFO', f'签名订单API响应: {response.status_code}', {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'url': url
            })
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    self.log('SUCCESS', '获取到订单响应数据', json_data)
                    
                    # 查找支付宝链接
                    self.extract_payment_links(json_data)
                    
                except json.JSONDecodeError:
                    self.log('WARNING', '响应不是有效的JSON格式', {
                        'content': response.text[:500]
                    })
            else:
                self.log('ERROR', f'API请求失败: {response.status_code}', {
                    'content': response.text[:500]
                })
                
            return response
            
        except Exception as e:
            self.log('ERROR', f'签名订单API请求失败: {e}')
            return None

    def generate_encrypted_data(self, phone):
        """生成加密数据（模拟）"""
        # 这里应该是真实的加密逻辑，现在用模拟数据
        import base64
        data = {
            'phone': phone,
            'product': 'vip_month',
            'amount': 19.8,
            'timestamp': int(time.time())
        }
        return base64.b64encode(json.dumps(data).encode()).decode()

    def generate_signature(self):
        """生成签名（模拟）"""
        import hashlib
        timestamp = str(int(time.time()))
        return hashlib.md5(f"{self.channel_code}{timestamp}".encode()).hexdigest()

    def generate_uuid(self):
        """生成UUID"""
        import uuid
        return str(uuid.uuid4())

    def extract_payment_links(self, data):
        """提取支付链接"""
        self.log('INFO', '🔗 提取支付链接')
        
        def search_for_links(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    if isinstance(value, str):
                        if 'alipay' in value.lower() or '支付宝' in value:
                            self.log('SUCCESS', f'发现支付宝链接 ({current_path})', {
                                'path': current_path,
                                'url': value
                            })
                            self.analyze_payment_url(value)
                        elif 'http' in value and any(keyword in value.lower() for keyword in ['pay', 'order', 'cashier']):
                            self.log('INFO', f'发现支付相关链接 ({current_path})', {
                                'path': current_path,
                                'url': value
                            })
                    elif isinstance(value, (dict, list)):
                        search_for_links(value, current_path)
                        
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    search_for_links(item, current_path)
        
        search_for_links(data)

    def analyze_payment_url(self, url):
        """分析支付URL"""
        self.log('INFO', f'🔍 分析支付URL: {url}')
        
        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)
            
            analysis = {
                'scheme': parsed.scheme,
                'domain': parsed.netloc,
                'path': parsed.path,
                'parameters': {k: v[0] if v else None for k, v in params.items()}
            }
            
            self.log('SUCCESS', '支付URL分析完成', analysis)
            
            # 如果是支付宝链接，尝试调用
            if 'alipay' in url.lower():
                self.test_alipay_redirect(url)
                
        except Exception as e:
            self.log('ERROR', f'支付URL分析失败: {e}')

    def test_alipay_redirect(self, url):
        """测试支付宝跳转"""
        self.log('INFO', '🚀 测试支付宝跳转')
        
        try:
            # 不实际访问支付宝，只分析链接
            self.log('SUCCESS', '支付宝链接已准备就绪', {
                'alipay_url': url,
                'action': '可以在移动设备上打开此链接完成支付'
            })
            
        except Exception as e:
            self.log('ERROR', f'支付宝跳转测试失败: {e}')

    def run_full_test(self, phone='13802913949'):
        """运行完整测试"""
        self.log('INFO', '🚀 开始完整支付流程测试')
        
        # 1. 测试H5页面
        h5_response = self.test_h5_page(phone)
        
        # 2. 测试签名订单API
        if h5_response:
            time.sleep(1)
            order_response = self.test_sign_order_api(phone)
        
        # 3. 保存结果
        self.save_results()

    def save_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"payment_test_{timestamp}.json"
        
        results = {
            'timestamp': timestamp,
            'total_logs': len(self.logs),
            'logs': self.logs
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.log('SUCCESS', f'测试结果已保存: {filename}')
            
        except Exception as e:
            self.log('ERROR', f'保存结果失败: {e}')

def main():
    """主函数"""
    print("🔍 简化版支付流程监控器")
    print("=" * 50)
    
    monitor = SimplePaymentMonitor()
    
    # 获取用户输入的手机号
    phone = input("请输入手机号 (默认: 13802913949): ").strip()
    if not phone:
        phone = '13802913949'
    
    try:
        monitor.run_full_test(phone)
        print("\n✅ 测试完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
