#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支付宝链接分析器
分析现有的支付宝链接结构，提取参数规律
"""

from urllib.parse import urlparse, parse_qs, unquote
import json

class AlipayLinkAnalyzer:
    def __init__(self):
        # 示例链接 - 从监控中捕获的真实链接
        self.sample_link = "alipays://platformapi/startapp?appId=20000067&url=https%3A%2F%2Fvip.iqiyi.com%2Fhtml5VIP%2Factivity%2FquickPayWrap%2Findex.html%3Ffv%3Dbd11cc1a65ac0128%26orderNo%3D202508111500590121436%26payMethod%3Dalipay%26sign%3D9c9efed313542d2a5f2c5025a726a861%26skuId%3Dsku_555029368976707642"

    def analyze_link(self, link):
        """分析支付宝链接"""
        print(f"🔍 分析链接: {link}")
        print("=" * 80)
        
        # 解析主链接
        parsed = urlparse(link)
        
        print(f"协议: {parsed.scheme}")
        print(f"域名: {parsed.netloc}")
        print(f"路径: {parsed.path}")
        
        # 解析查询参数
        params = parse_qs(parsed.query)
        print(f"\n📋 主要参数:")
        
        for key, values in params.items():
            value = values[0] if values else ''
            print(f"  {key}: {value}")
            
            # 如果是URL参数，进一步解析
            if key == 'url' and value:
                print(f"\n🔗 解析嵌套URL:")
                self.analyze_nested_url(value)
        
        return {
            'scheme': parsed.scheme,
            'netloc': parsed.netloc,
            'path': parsed.path,
            'params': {k: v[0] if v else '' for k, v in params.items()}
        }

    def analyze_nested_url(self, encoded_url):
        """分析嵌套的URL"""
        # URL解码
        decoded_url = unquote(encoded_url)
        print(f"  解码后URL: {decoded_url}")
        
        # 解析嵌套URL
        nested_parsed = urlparse(decoded_url)
        nested_params = parse_qs(nested_parsed.query)
        
        print(f"  协议: {nested_parsed.scheme}")
        print(f"  域名: {nested_parsed.netloc}")
        print(f"  路径: {nested_parsed.path}")
        print(f"  参数:")
        
        for key, values in nested_params.items():
            value = values[0] if values else ''
            print(f"    {key}: {value}")
        
        return {
            'decoded_url': decoded_url,
            'scheme': nested_parsed.scheme,
            'netloc': nested_parsed.netloc,
            'path': nested_parsed.path,
            'params': {k: v[0] if v else '' for k, v in nested_params.items()}
        }

    def extract_patterns(self, link):
        """提取链接模式"""
        analysis = self.analyze_link(link)
        
        print(f"\n🎯 提取的模式:")
        print("=" * 80)
        
        # 支付宝固定部分
        print(f"支付宝协议: {analysis['scheme']}://")
        print(f"API路径: {analysis['netloc']}{analysis['path']}")
        print(f"应用ID: {analysis['params'].get('appId', 'N/A')}")
        
        # 爱奇艺URL部分
        if 'url' in analysis['params']:
            nested_url = unquote(analysis['params']['url'])
            nested_analysis = self.analyze_nested_url(analysis['params']['url'])
            
            print(f"\n爱奇艺基础URL: {nested_analysis['scheme']}://{nested_analysis['netloc']}{nested_analysis['path']}")
            print(f"关键参数:")
            
            key_params = ['fv', 'orderNo', 'payMethod', 'sign', 'skuId']
            for param in key_params:
                value = nested_analysis['params'].get(param, 'N/A')
                print(f"  {param}: {value}")
                
                # 分析参数特征
                if param == 'orderNo':
                    print(f"    订单号格式: {len(value)}位, 前14位可能是时间戳")
                elif param == 'fv':
                    print(f"    FV格式: {len(value)}位十六进制字符串")
                elif param == 'sign':
                    print(f"    签名格式: {len(value)}位十六进制字符串 (可能是MD5)")
                elif param == 'skuId':
                    print(f"    SKU格式: {value}")

    def generate_template(self):
        """生成链接模板"""
        print(f"\n📝 链接生成模板:")
        print("=" * 80)
        
        template = {
            "alipay_scheme": "alipays://platformapi/startapp",
            "app_id": "20000067",
            "iqiyi_base_url": "https://vip.iqiyi.com/html5VIP/activity/quickPayWrap/index.html",
            "required_params": {
                "fv": "16位十六进制字符串 (可能基于时间戳生成)",
                "orderNo": "20位数字 (YYYYMMDDHHMMSS + 6位随机数)",
                "payMethod": "固定值: alipay",
                "sign": "32位MD5签名 (基于订单号、SKU等参数)",
                "skuId": "产品SKU ID"
            },
            "sku_examples": {
                "sku_555029368976707642": "爱奇艺VIP月卡",
                "sku_555029368976707643": "爱奇艺VIP年卡 (推测)",
                "sku_555029368976707644": "爱奇艺体育会员 (推测)"
            }
        }
        
        print(json.dumps(template, ensure_ascii=False, indent=2))
        
        return template

    def validate_link_format(self, link):
        """验证链接格式"""
        print(f"\n✅ 验证链接格式:")
        print("=" * 80)
        
        checks = []
        
        # 检查协议
        if link.startswith('alipays://'):
            checks.append("✅ 协议正确")
        else:
            checks.append("❌ 协议错误")
        
        # 检查API路径
        if 'platformapi/startapp' in link:
            checks.append("✅ API路径正确")
        else:
            checks.append("❌ API路径错误")
        
        # 检查必要参数
        required_params = ['appId', 'url']
        for param in required_params:
            if f'{param}=' in link:
                checks.append(f"✅ 包含{param}参数")
            else:
                checks.append(f"❌ 缺少{param}参数")
        
        # 检查嵌套URL
        if 'vip.iqiyi.com' in link:
            checks.append("✅ 包含爱奇艺URL")
        else:
            checks.append("❌ 缺少爱奇艺URL")
        
        for check in checks:
            print(f"  {check}")
        
        return all('✅' in check for check in checks)

def main():
    """主函数"""
    analyzer = AlipayLinkAnalyzer()
    
    print("🔍 支付宝链接分析器")
    print("=" * 80)
    
    # 分析示例链接
    print("📱 分析示例链接:")
    analyzer.analyze_link(analyzer.sample_link)
    
    # 提取模式
    analyzer.extract_patterns(analyzer.sample_link)
    
    # 生成模板
    analyzer.generate_template()
    
    # 验证格式
    analyzer.validate_link_format(analyzer.sample_link)
    
    print(f"\n🎯 关键发现:")
    print("=" * 80)
    print("1. 支付宝链接使用 alipays:// 协议")
    print("2. 通过 platformapi/startapp 调用小程序")
    print("3. appId=20000067 是固定的应用ID")
    print("4. 嵌套的爱奇艺URL包含订单和签名信息")
    print("5. 订单号格式: YYYYMMDDHHMMSS + 6位随机数")
    print("6. 签名可能是MD5算法生成")
    print("7. SKU ID决定了购买的产品类型")

if __name__ == "__main__":
    main()
