{"log": {"entries": [{"request": {"method": "GET", "bodySize": 0, "headersSize": 1293, "postData": {"params": [], "text": "", "mimeType": "application/x-www-form-urlencoded; charset=utf-8"}, "cookies": [], "headers": [{"name": "Host", "value": "guide-acs.m.taobao.com"}, {"name": "x-app-conf-v", "value": "0"}, {"name": "x-bx-version", "value": "6.5.80"}, {"name": "x-pv", "value": "6.3"}, {"name": "User-Agent", "value": "MTOPSDK%2F1.9.3.48%20%28iOS%3B18.6%3BApple%3BiPhone17%2C1%29"}, {"name": "x-c-traceid", "value": "Z4uS34yJfFkDAMki8hqgWffp1754892766000014287"}, {"name": "x-page-name", "value": "TBHomeViewController"}, {"name": "f-refer", "value": "mtop"}, {"name": "x-sgext", "value": "JAuemw4BwBEK%2B68HxDnra5urq6%2B4qqmmrL2qvbivrKuvpqKsrKitvauuq66rrquuq66rrquuq664rriuuK6rvauuq664rrivuK%2B4r7ivuK%2B4r7iuuK6rrquuuKz%2F%2F7iuuKa4rg%3D%3D"}, {"name": "x-app-ver", "value": "10.7.66"}, {"name": "x-utdid", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "x-cmd-v", "value": "0%7C0"}, {"name": "x-devid", "value": "ApIINvGxyZZT1BQwvwyxElf8Nry0KDPzZoGxzyEUq-bD"}, {"name": "Connection", "value": "keep-alive"}, {"name": "x-sign", "value": "izDdOU002xAALlzUrD6NV2wyFR2szlzeUtd0aCA4PH2xcnhtBzrvdmETn45c3bctp9CM3lQTb2SXZFia%2F68Yas2qyn5czlzeXM5c3l"}, {"name": "x-mini-wua", "value": "iGATWMBGBKpVTWzGpGRN3AiYGNg%2FMlV8gXKYIULI9CCaQWTjEwy02bNWw8PBBb8yZRN3sPC0hP5NvWKttBUsnn7DKSErVyIpObTSx7iXgOSF9xswPxAbELeYPdsHqbxNeMzzWpe0XIHXhelF7cLMxgS70"}, {"name": "x-appkey", "value": "23086819"}, {"name": "x-features", "value": "11"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "x-umt", "value": "2sJLNb5LOoPc3DUAAD6%2FP7DtAACM0zus"}, {"name": "c-launch-info", "value": "(null),0,1754892766215.989,0"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/x-www-form-urlencoded; charset=utf-8"}, {"name": "x-ttid", "value": "apple-iphone%40alipay_iphone_10.7.66.6000"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "x-t", "value": "1754892766"}], "queryString": [{"name": "rnd", "value": "2DEE97AA9C2782D4951CDC16A4CE5180"}, {"name": "data", "value": "%7B%7D"}], "httpVersion": "HTTP/1.1", "url": "https://guide-acs.m.taobao.com/gw/mtop.common.gettimestamp/*?rnd=2DEE97AA9C2782D4951CDC16A4CE5180&data=%7B%7D"}, "timings": {"connect": 378.9999485015869, "send": 275.00009536743164, "dns": -1, "ssl": 272.0000743865967, "wait": 46.000003814697266, "blocked": -1, "receive": 0.9999275207519531}, "serverIPAddress": "***********", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "Tengine/AServer-Ingress/3.0.36"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Content-Type", "value": "application/json;charset=UTF-8"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "x-paramkey", "value": "mtop.common.getTimestamp"}, {"name": "Content-length", "value": "109"}, {"name": "x-aserver-sret", "value": "SUCCESS"}, {"name": "Ups-Target-Key", "value": "mtop.uncenter.mytime"}, {"name": "X-protocol", "value": "HTTP/1.1"}, {"name": "EagleEye-TraceId", "value": "214784d117548927669201682e1117"}, {"name": "Strict-Transport-Security", "value": "max-age=0"}, {"name": "s-rt", "value": "1"}, {"name": "s-cunit", "value": "0"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"api\":\"mtop.common.getTimestamp\",\"v\":\"*\",\"ret\":[\"SUCCESS::接口调用成功\"],\"data\":{\"t\":\"1754892766921\"}}", "size": 109, "mimeType": "application/json;charset=UTF-8"}}, "time": 46.99993133544922, "startedDateTime": "2025-08-11T14:12:46.628Z"}, {"request": {"method": "POST", "bodySize": 581, "headersSize": 499, "postData": {"params": [], "text": "D-MM,2025-08-11 14:12:45:930,IPHONE_1ND,10.7.66.6000,3,460000000000000|dzj9p3gw5uopc5g,30C0DAA2-1EF6-4A87-B1E1-37AAE9619815,2088102032328461,,,1000,keybiz<PERSON>ce,BizCanNotUse,BIZ_FRAME,DESCRIPTION_NOT_FOUND_SERVICE,APActionableNotificationService,region=CN,iOS,18.6,WIFI|--,iPhone17 1,,,Z4uS34yJfFkDAMki8hqgWffp,zh-Hans,,,,66900027,,VoiceOver=0^TimeZone=Asia/Shanghai^AppStatus=background^CIP=(null)^AppSession=3055AFCB-23F4-40FB-BC3E-FC455302866B^appMode=normal^editionId=300003^lowEnd=F^QOS=1^buildNumber=22G86^simC=2,,1206*2622,20000001,,9,2,********-51F3-44D8-B0A9-************$$", "mimeType": "text/xml"}, "cookies": [], "headers": [{"name": "Host", "value": "datagw-edge.alipay.com"}, {"name": "utdId", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "userId", "value": "2088102032328461"}, {"name": "event", "value": "max<PERSON><PERSON><PERSON><PERSON>nt"}, {"name": "productVersion", "value": "10.7.66.6000"}, {"name": "taskId", "value": "4"}, {"name": "bizCode", "value": "keybiztrace"}, {"name": "Accept-Language", "value": ""}, {"name": "X-Gray<PERSON><PERSON>", "value": "AbWUAAAAACDAAABAAAAAAkAAAEAAAAAA|AgAAlLUARxUeHzZRXnY="}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Content-Length", "value": "472"}, {"name": "User-Agent", "value": ""}, {"name": "Connection", "value": "keep-alive"}, {"name": "X-Flow-Control", "value": "1"}, {"name": "Content-Type", "value": "text/xml"}, {"name": "<PERSON><PERSON>", "value": ""}, {"name": "productId", "value": "IPHONE_1ND"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://datagw-edge.alipay.com/loggw/logUpload.do"}, "timings": {"connect": 203.00006866455078, "send": 172.99985885620117, "dns": -1, "ssl": 168.99991035461426, "wait": 144.99998092651367, "blocked": -1, "receive": 2.000093460083008}, "serverIPAddress": "***************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "Tengin<PERSON>"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Content-Length", "value": "26"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "Status-Code", "value": "3000"}, {"name": "Via", "value": "metds-43-1854[200], cache17.cn6382[89,0]"}, {"name": "Timing-Allow-Origin", "value": "*"}, {"name": "EagleId", "value": "78e9b1a517548927666328970e"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":200,\"code_v2\":200}", "size": 26, "mimeType": "text/plain"}}, "time": 148.9999294281006, "startedDateTime": "2025-08-11T14:12:46.325Z"}, {"request": {"method": "POST", "bodySize": 581, "headersSize": 499, "postData": {"params": [], "text": "D-MM,2025-08-11 14:12:45:944,IPHONE_1ND,10.7.66.6000,3,460000000000000|dzj9p3gw5uopc5g,30C0DAA2-1EF6-4A87-B1E1-37AAE9619815,2088102032328461,,,1000,keybiz<PERSON>ce,BizCanNotUse,BIZ_FRAME,DESCRIPTION_NOT_FOUND_SERVICE,APPushNotificationGuideService,region=CN,iOS,18.6,WIFI|--,iPhone17 1,,,Z4uS34yJfFkDAMki8hqgWffp,zh-Hans,,,,66900027,,VoiceOver=0^TimeZone=Asia/Shanghai^AppStatus=background^CIP=(null)^AppSession=3055AFCB-23F4-40FB-BC3E-FC455302866B^appMode=normal^editionId=300003^lowEnd=F^QOS=1^buildNumber=22G86^simC=2,,1206*2622,20000001,,14,2,********-51F3-44D8-B0A9-************$$", "mimeType": "text/xml"}, "cookies": [], "headers": [{"name": "Host", "value": "datagw-edge.alipay.com"}, {"name": "utdId", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "userId", "value": "2088102032328461"}, {"name": "event", "value": "max<PERSON><PERSON><PERSON><PERSON>nt"}, {"name": "productVersion", "value": "10.7.66.6000"}, {"name": "taskId", "value": "7"}, {"name": "bizCode", "value": "keybiztrace"}, {"name": "Accept-Language", "value": ""}, {"name": "X-Gray<PERSON><PERSON>", "value": "AbWUAAAAACDAAABAAAAAAkAAAEAAAAAA|AgAAlLUARxUeHzZRXnY="}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Content-Length", "value": "472"}, {"name": "User-Agent", "value": ""}, {"name": "Connection", "value": "keep-alive"}, {"name": "X-Flow-Control", "value": "1"}, {"name": "Content-Type", "value": "text/xml"}, {"name": "<PERSON><PERSON>", "value": ""}, {"name": "productId", "value": "IPHONE_1ND"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://datagw-edge.alipay.com/loggw/logUpload.do"}, "timings": {"connect": 187.99996376037598, "send": 166.0001277923584, "dns": -1, "ssl": 157.99999237060547, "wait": 134.99999046325684, "blocked": -1, "receive": 3.000020980834961}, "serverIPAddress": "***************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "Tengin<PERSON>"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Content-Length", "value": "26"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "Status-Code", "value": "3000"}, {"name": "Via", "value": "metds-43-64[200], cache18.cn6382[82,0]"}, {"name": "Timing-Allow-Origin", "value": "*"}, {"name": "EagleId", "value": "78e9b1a617548927666245345e"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":200,\"code_v2\":200}", "size": 26, "mimeType": "text/plain"}}, "time": 143.00012588500977, "startedDateTime": "2025-08-11T14:12:46.317Z"}, {"request": {"method": "POST", "bodySize": 0, "headersSize": 537, "postData": {"mimeType": "text/xml"}, "cookies": [], "headers": [{"name": "Host", "value": "datagw-edge.alipay.com"}, {"name": "userId", "value": "2088102032328461"}, {"name": "taskId", "value": "5"}, {"name": "bizCode", "value": "storyline"}, {"name": "User-Agent", "value": ""}, {"name": "<PERSON><PERSON>", "value": ""}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "X-Flow-Control", "value": "1"}, {"name": "Content-Length", "value": "782"}, {"name": "event", "value": "max<PERSON><PERSON><PERSON><PERSON>nt"}, {"name": "X-Content-Type", "value": "application/x-protobuf"}, {"name": "Connection", "value": "keep-alive"}, {"name": "X-Gray<PERSON><PERSON>", "value": "AbWUAAAAACDAAABAAAAAAkAAAEAAAAAA|AgAAlLUARxUeHzZRXnY="}, {"name": "Accept-Language", "value": ""}, {"name": "productId", "value": "IPHONE_1ND"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "text/xml"}, {"name": "productVersion", "value": "10.7.66.6000"}, {"name": "utdId", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://datagw-edge.alipay.com/loggw/logUpload.do"}, "timings": {"connect": 197.9999542236328, "send": 167.9999828338623, "dns": -1, "ssl": 160.00008583068848, "wait": 133.00013542175293, "blocked": -1, "receive": 0.9999275207519531}, "serverIPAddress": "***************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "Tengin<PERSON>"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Content-Length", "value": "26"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "Status-Code", "value": "3000"}, {"name": "Via", "value": "metds-43-1836[200], cache4.cn6382[81,0]"}, {"name": "Timing-Allow-Origin", "value": "*"}, {"name": "EagleId", "value": "78e9b19817548927666262492e"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":200,\"code_v2\":200}", "size": 26, "mimeType": "text/plain"}}, "time": 138.99993896484375, "startedDateTime": "2025-08-11T14:12:46.315Z"}, {"request": {"method": "POST", "bodySize": 586, "headersSize": 499, "postData": {"params": [], "text": "D-MM,2025-08-11 14:12:45:911,IPHONE_1ND,10.7.66.6000,3,460000000000000|dzj9p3gw5uopc5g,30C0DAA2-1EF6-4A87-B1E1-37AAE9619815,2088102032328461,,,1000,keybiz<PERSON>ce,BizCanNotUse,BIZ_FRAME,DESCRIPTION_NOT_FOUND_SERVICE,RemoteNotificationsURLHandlerService,region=CN,iOS,18.6,WIFI|--,iPhone17 1,,,Z4uS34yJfFkDAMki8hqgWffp,zh-Hans,,,,66900027,,VoiceOver=0^TimeZone=Asia/Shanghai^AppStatus=background^CIP=(null)^AppSession=3055AFCB-23F4-40FB-BC3E-FC455302866B^appMode=normal^editionId=300003^lowEnd=F^QOS=4^buildNumber=22G86^simC=2,,1206*2622,20000001,,7,2,********-51F3-44D8-B0A9-************$$", "mimeType": "text/xml"}, "cookies": [], "headers": [{"name": "Host", "value": "datagw-edge.alipay.com"}, {"name": "utdId", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "userId", "value": "2088102032328461"}, {"name": "event", "value": "max<PERSON><PERSON><PERSON><PERSON>nt"}, {"name": "productVersion", "value": "10.7.66.6000"}, {"name": "taskId", "value": "3"}, {"name": "bizCode", "value": "keybiztrace"}, {"name": "Accept-Language", "value": ""}, {"name": "X-Gray<PERSON><PERSON>", "value": "AbWUAAAAACDAAABAAAAAAkAAAEAAAAAA|AgAAlLUARxUeHzZRXnY="}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Content-Length", "value": "478"}, {"name": "User-Agent", "value": ""}, {"name": "Connection", "value": "keep-alive"}, {"name": "X-Flow-Control", "value": "1"}, {"name": "Content-Type", "value": "text/xml"}, {"name": "<PERSON><PERSON>", "value": ""}, {"name": "productId", "value": "IPHONE_1ND"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://datagw-edge.alipay.com/loggw/logUpload.do"}, "timings": {"connect": 188.00020217895508, "send": 161.99994087219238, "dns": -1, "ssl": 150.00009536743164, "wait": 114.00008201599121, "blocked": -1, "receive": 3.000020980834961}, "serverIPAddress": "***************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "Tengin<PERSON>"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Content-Length", "value": "26"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "Status-Code", "value": "3000"}, {"name": "Via", "value": "metds-49-192850[200], cache1.cn6382[82,0]"}, {"name": "Timing-Allow-Origin", "value": "*"}, {"name": "EagleId", "value": "78e9b19517548927666021882e"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":200,\"code_v2\":200}", "size": 26, "mimeType": "text/plain"}}, "time": 125, "startedDateTime": "2025-08-11T14:12:46.302Z"}, {"request": {"method": "POST", "bodySize": 138, "headersSize": 979, "postData": {"params": [], "text": "\n\f10.7.66.6000\u0012\u0018Z4uS34yJfFkDAMki8hqgWffp\u001a\u00102088102032328461\"\fapple-iphone*\u000fdzj9p3gw5uopc5g:\u0003iosJ\nIPHONE_1NDZ\u0005appleb\niPhone17,1j\u000418.6r\u0005apple", "mimeType": "application/protobuf"}, "cookies": [{"name": "zone", "value": "RZ54B"}, {"name": "ALIPAYJSESSIONID", "value": "RZ54u4aQjrfpiJiW0IN5cKV1Ua6VFU46mobilegwRZ54"}, {"name": "devKeySet", "value": "{\"apdidToken\":\"7tkM2dpK3Hz7HrlscatoHw72q0lYeNG6L5HiJDDVPog2FZuSkriUmAEB\"}"}], "headers": [{"name": "Host", "value": "mgw.alipay.com"}, {"name": "userId", "value": "2088102032328461"}, {"name": "AppId", "value": "IPHONE_1ND"}, {"name": "clientVersion", "value": "10.7.66.6000"}, {"name": "Did", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "x-mgw-extension", "value": "1"}, {"name": "Ts", "value": "PYNmei2"}, {"name": "<PERSON><PERSON>", "value": "zone=RZ54B; ALIPAYJSESSIONID=RZ54u4aQjrfpiJiW0IN5cKV1Ua6VFU46mobilegwRZ54; devKeySet={\"apdidToken\":\"7tkM2dpK3Hz7HrlscatoHw72q0lYeNG6L5HiJDDVPog2FZuSkriUmAEB\"}"}, {"name": "User-Agent", "value": "AlipayWallet/10.7.66.6000 CFNetwork/3826.600.41 Darwin/24.6.0"}, {"name": "visibleflag", "value": "1"}, {"name": "mini<PERSON>a", "value": "{\"x\":\"AAAA_nlzfuuL6mJ3omLUvELbFAmlNpaUYgBynqD7v7zM\\/ES9o6tWE3u0Fb1U8F9aM0dfk\"}"}, {"name": "Skip-Monitor", "value": "1"}, {"name": "retryable2", "value": "1"}, {"name": "x-ant-i18n-support", "value": "true"}, {"name": "clientAppMode", "value": "normal"}, {"name": "Content-Length", "value": "138"}, {"name": "Sign", "value": "fa42b4f34960e133be4ffe1ec8fd4314"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Operation-Type", "value": "alipay.client.interfere.config.get"}, {"name": "Authorization", "value": "x-sid RZ54u4aQjrfpiJiW0IN5cKV1Ua6VFU46mobilegwRZ54"}, {"name": "Accept-Language", "value": "zh-Hans"}, {"name": "x-mgw-client-env", "value": "wifi,4_0.000000,fg"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/protobuf"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "scene", "value": "active"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://mgw.alipay.com/mgw.htm"}, "timings": {"connect": 332.0000171661377, "send": 203.00006866455078, "dns": -1, "ssl": 190.00005722045898, "wait": 171.99993133544922, "blocked": -1, "receive": 0.9999275207519531}, "serverIPAddress": "***************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "<PERSON>nner"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Content-Type", "value": "text/plain; charset=utf-8"}, {"name": "Content-Length", "value": "31"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Mgw-TraceId", "value": "0b96615917548927666501635ea73a"}, {"name": "Result-Status", "value": "1000"}, {"name": "Memo", "value": "ok"}, {"name": "Tips", "value": "ok"}, {"name": "<PERSON><PERSON>", "value": "1"}, {"name": "Set-<PERSON><PERSON>", "value": "zone=RZ43B; Path=/; Domain=alipay.com"}, {"name": "Server-Time", "value": "1754892766689"}, {"name": "X-Http-Version", "value": "1.1"}, {"name": "Mmtp-Ext-Utc", "value": "38"}, {"name": "Via", "value": "spanner-mobile-v2-cz20m-634.ea179[200]"}], "httpVersion": "HTTP/1.1", "content": {"text": "\b\u0000\"\r1753173205000(\u00002\nIPHONE_1ND", "size": 31, "mimeType": "text/plain; charset=utf-8"}}, "time": 182.999849319458, "startedDateTime": "2025-08-11T14:12:46.295Z"}, {"request": {"method": "POST", "bodySize": 526, "headersSize": 499, "postData": {"params": [], "text": "D-MM,2025-08-11 14:12:45:885,IPHONE_1ND,10.7.66.6000,3,460000000000000|dzj9p3gw5uopc5g,30C0DAA2-1EF6-4A87-B1E1-37AAE9619815,2088102032328461,,,1000,keybiz<PERSON><PERSON>,BizCanNotUse,BIZ_FRAME,KSCRASH_REPORT_OPEN,239,,iOS,18.6,WIFI|--,iPhone17 1,,,Z4uS34yJfFkDAMki8hqgWffp,zh-Hans,,,,66900027,,VoiceOver=0^TimeZone=Asia/Shanghai^AppStatus=background^CIP=(null)^AppSession=3055AFCB-23F4-40FB-BC3E-FC455302866B^appMode=normal^editionId=300003^lowEnd=F^QOS=4^buildNumber=22G86^simC=2,,1206*2622,,,4,2,********-51F3-44D8-B0A9-************$$", "mimeType": "text/xml"}, "cookies": [], "headers": [{"name": "Host", "value": "datagw-edge.alipay.com"}, {"name": "utdId", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "userId", "value": "2088102032328461"}, {"name": "event", "value": "max<PERSON><PERSON><PERSON><PERSON>nt"}, {"name": "productVersion", "value": "10.7.66.6000"}, {"name": "taskId", "value": "2"}, {"name": "bizCode", "value": "keybiztrace"}, {"name": "Accept-Language", "value": ""}, {"name": "X-Gray<PERSON><PERSON>", "value": "AbWUAAAAACDAAABAAAAAAkAAAEAAAAAA|AgAAlLUARxUeHzZRXnY="}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Content-Length", "value": "435"}, {"name": "User-Agent", "value": ""}, {"name": "Connection", "value": "keep-alive"}, {"name": "X-Flow-Control", "value": "1"}, {"name": "Content-Type", "value": "text/xml"}, {"name": "<PERSON><PERSON>", "value": ""}, {"name": "productId", "value": "IPHONE_1ND"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://datagw-edge.alipay.com/loggw/logUpload.do"}, "timings": {"connect": 183.0000877380371, "send": 163.00010681152344, "dns": -1, "ssl": 141.00003242492676, "wait": 105.99994659423828, "blocked": -1, "receive": 6.000041961669922}, "serverIPAddress": "***************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "Tengin<PERSON>"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Content-Length", "value": "26"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "Status-Code", "value": "3000"}, {"name": "Via", "value": "metds-43-1858[200], cache12.cn6382[93,0]"}, {"name": "Timing-Allow-Origin", "value": "*"}, {"name": "EagleId", "value": "78e9b1a017548927665854112e"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":200,\"code_v2\":200}", "size": 26, "mimeType": "text/plain"}}, "time": 131.99996948242188, "startedDateTime": "2025-08-11T14:12:46.288Z"}, {"request": {"method": "GET", "bodySize": 0, "headersSize": 402, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "mdap.alipay.com"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept", "value": "*/*"}, {"name": "User-Agent", "value": "AlipayWallet/10.7.66.6000 CFNetwork/3826.600.41 Darwin/24.6.0"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "bizCode", "value": "LogConfig"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [{"name": "userId", "value": "2088102032328461"}, {"name": "productId", "value": "IPHONE_1ND"}, {"name": "productVersion", "value": "10.7.66.6000"}, {"name": "utdId", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "templateId", "value": "2.0"}, {"name": "configVersion", "value": "2"}], "httpVersion": "HTTP/1.1", "url": "https://mdap.alipay.com/loggw/diagnosisConfig.do?userId=2088102032328461&productId=IPHONE_1ND&productVersion=10.7.66.6000&utdId=Z4uS34yJfFkDAMki8hqgWffp&templateId=2.0&configVersion=2"}, "timings": {"connect": 380.00011444091797, "send": 316.9999122619629, "dns": -1, "ssl": 315.000057220459, "wait": 69.0000057220459, "blocked": -1, "receive": 11.00015640258789}, "serverIPAddress": "***********", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Content-Type", "value": "application/json;charset=UTF-8"}, {"name": "Content-Length", "value": "80"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Strict-Transport-Security", "value": "max-age=31536000"}, {"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=F3F5115F95811C17C973CE7946DFDCCF; Path=/; HttpOnly"}, {"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=F3F5115F95811C17C973CE7946DFDCCF; Path=; Secure; HttpOnly"}, {"name": "Via", "value": "mdap-43-400[200], metds-49-190943[200]"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"200\",\"content\":{\"positiveDiagnoseLog\":{\"send\":[\"none\"],\"write\":\"yes\"}}}", "size": 80, "mimeType": "application/json;charset=UTF-8"}}, "time": 80.00016212463379, "startedDateTime": "2025-08-11T14:12:46.261Z"}, {"request": {"method": "POST", "bodySize": 704, "headersSize": 364, "postData": {"params": [], "text": "3dI3IRh64anImWXYC+7SnAGAB6nngkdUNC472josAKAfPngfrmEis93CjCOJwrx7zb3o0kQhqov6yQUCOcMm/VXFQrthXbLH9PUnBQIQMx7l4VUpRWP/U1KYWq8dKT8PScTnQpQ2onrzZ7hKEEe0AdsGUqM6uf66n2iAVwyFENLluFG2n3qRkQe47T8ScZa2yAgLxozOApvgFNFWAFuxFC7ibHWp2CDFy6beEeCze2LE0nhqZsfrVYvyvX/FRpn7rQXlug1crOjIKlWaDeHvP4ktFel6kpJ+26nI6H8ovmlF+/90cgoH8TrB/U3cXwM4am4yGzcKf36RaxDrGvcCMuxo3QIt1XhhIoHlCihn0roH8u0ZA1xkOsJw2cift+5wF6zhriUXBUigVQfCzDwEuCdsNR5jNX0uKeLsUrLNJRJE8g8qJHD0S0RgRHP9CHUIvLHJcLiJXQFRoerREoUn3wcWRIGCZNJCp2+PL4XEelS/HzuTEOUuHlSltzufHCBYQ6Qg3iSTRBOW7ZUzqel6pvS9PLD191tY5L8jqQvT+qeSF7QUltbvT0I//92EZEmtkOzPpRYafvd7p7z+UcoZvtoO/Lf+LUDAfH8jTU7B9YlAdVg95dLLuqc6XuLBffod9yaTKdtGk/nL3HGotX0inx1VANG2LRT5vToCyOgOIDnTS55vvp8oEVGbJpayQCIK", "mimeType": "text/plain"}, "cookies": [], "headers": [{"name": "Host", "value": "cdn.ynuf.aliapp.org"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Accept", "value": "*/*"}, {"name": "User-Agent", "value": "AlipayWallet/10.7.66.6000 CFNetwork/3826.600.41 Darwin/24.6.0"}, {"name": "Content-Length", "value": "704"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}], "queryString": [{"name": "pn", "value": "com.alipay.iphoneclient"}, {"name": "pv", "value": "10.7.66"}, {"name": "pt", "value": "1"}, {"name": "nv", "value": "3.0"}, {"name": "os", "value": "1"}, {"name": "os2", "value": "i"}, {"name": "sv", "value": "2.1.162"}], "httpVersion": "HTTP/1.1", "url": "https://cdn.ynuf.aliapp.org/u6vr/g9m6/1gkepoi?pn=com.alipay.iphoneclient&pv=10.7.66&pt=1&nv=3.0&os=1&os2=i&sv=2.1.162"}, "timings": {"connect": 279.9999713897705, "send": 250, "dns": -1, "ssl": 246.0000514984131, "wait": 138.99993896484375, "blocked": -1, "receive": 2.000093460083008}, "serverIPAddress": "**************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "Tengin<PERSON>"}, {"name": "Content-Type", "value": "application/json;charset=utf-8"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Via", "value": "cache18.cn7771[105,0]"}, {"name": "Timing-Allow-Origin", "value": "*"}, {"name": "EagleId", "value": "78e8f3a617548927664784923e"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"ck\":\"dab89473893af54ebea7f532ed84efa4\",\"ec\":200,\"dt\":\"wv7yhEjYUL2gFlMrOeJDjgW+oFegdrCdNIWXeN+WxcCI0i16psUJV46kcwKkDQ/6\"}", "size": 122, "mimeType": "application/json;charset=utf-8"}}, "time": 143.00012588500977, "startedDateTime": "2025-08-11T14:12:46.172Z"}, {"request": {"method": "GET", "bodySize": 0, "headersSize": 402, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "gw.alipayobjects.com"}, {"name": "Connection", "value": "keep-alive"}, {"name": "If-None-Match", "value": "fcb39f9f1ee8641fee46d48a9a713878"}, {"name": "Accept", "value": "*/*"}, {"name": "User-Agent", "value": "AlipayWallet/10.7.66.6000 CFNetwork/3826.600.41 Darwin/24.6.0"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "bizCode", "value": "LogConfig"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}], "queryString": [{"name": "productId", "value": "IPHONE_1ND"}, {"name": "productVersion", "value": "10.7.66.6000"}, {"name": "templateId", "value": "2.0"}, {"name": "configVersion", "value": "2"}], "httpVersion": "HTTP/1.1", "url": "https://gw.alipayobjects.com/config/loggw/logConfig.do?productId=IPHONE_1ND&productVersion=10.7.66.6000&templateId=2.0&configVersion=2"}, "timings": {"connect": 245.00012397766113, "send": 216.00008010864258, "dns": -1, "ssl": 213.00005912780762, "wait": 24.99985694885254, "blocked": -1, "receive": 1.0001659393310547}, "serverIPAddress": "***************", "response": {"status": 304, "cookies": [], "content": {"size": 0, "text": ""}, "headers": [{"name": "Server", "value": "Tengin<PERSON>"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:28 GMT"}, {"name": "Strict-Transport-Security", "value": "max-age=31536000"}, {"name": "Via", "value": "mdap-43-468[304], metds-43-170[304], cache46.l2cn3008[35,8,304-0,C], cache76.l2cn3008[11,0], vcache13.cn5798[0,0,304-0,H], vcache10.cn5798[1,0]"}, {"name": "Cache-Control", "value": "max-age=180"}, {"name": "ETag", "value": "fcb39f9f1ee8641fee46d48a9a713878"}, {"name": "Age", "value": "18"}, {"name": "<PERSON>-Swift-Global-Savetime", "value": "1754892748"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "HIT TCP_IMS_HIT dirn:-2:-2"}, {"name": "X-Swift-SaveTime", "value": "Mon, 11 Aug 2025 06:12:28 GMT"}, {"name": "X-Swift-CacheTime", "value": "180"}, {"name": "Timing-Allow-Origin", "value": "*"}, {"name": "EagleId", "value": "78e9af9e17548927664272089e"}], "httpVersion": "HTTP/1.1"}, "time": 26.000022888183594, "startedDateTime": "2025-08-11T14:12:46.134Z"}, {"request": {"method": "POST", "bodySize": 530, "headersSize": 502, "postData": {"params": [], "text": "D-VM,2025-08-11 14:12:45:848,IPHONE_1ND,10.7.66.6000,3,460000000000000|dzj9p3gw5uopc5g,30C0DAA2-1EF6-4A87-B1E1-37AAE9619815,2088102032328461,event,,,,,,,BANDAGE,2,dynamicrelease,c,Process,Success,66900027,,,,Z4uS34yJfFkDAMki8hqgWffp,,,,,,,1000,iPhone17 1,18.6,WIFI|--,,,zh-Hans,66900027,,,,VoiceOver=0^TimeZone=Asia/Shanghai^AppStatus=background^CIP=(null)^AppSession=3055AFCB-23F4-40FB-BC3E-FC455302866B^appMode=normal^editionId=300003^lowEnd=F^QOS=4^buildNumber=22G86^simC=2,,1206*2622,,2,********-51F3-44D8-B0A9-************,$$", "mimeType": "text/xml"}, "cookies": [], "headers": [{"name": "Host", "value": "datagw-edge.alipay.com"}, {"name": "utdId", "value": "Z4uS34yJfFkDAMki8hqgWffp"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "userId", "value": "2088102032328461"}, {"name": "event", "value": "max<PERSON><PERSON><PERSON><PERSON>nt"}, {"name": "productVersion", "value": "10.7.66.6000"}, {"name": "taskId", "value": "1"}, {"name": "bizCode", "value": "dynamicrelease"}, {"name": "Accept-Language", "value": ""}, {"name": "X-Gray<PERSON><PERSON>", "value": "AbWUAAAAACDAAABAAAAAAkAAAEAAAAAA|AgAAlLUARxUeHzZRXnY="}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Content-Length", "value": "422"}, {"name": "User-Agent", "value": ""}, {"name": "Connection", "value": "keep-alive"}, {"name": "X-Flow-Control", "value": "1"}, {"name": "Content-Type", "value": "text/xml"}, {"name": "<PERSON><PERSON>", "value": ""}, {"name": "productId", "value": "IPHONE_1ND"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://datagw-edge.alipay.com/loggw/logUpload.do"}, "timings": {"connect": 197.00002670288086, "send": 231.00018501281738, "dns": -1, "ssl": 164.0000343322754, "wait": 49.99995231628418, "blocked": -1, "receive": 0.9999275207519531}, "serverIPAddress": "***************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "Tengin<PERSON>"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Content-Length", "value": "26"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:46 GMT"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "Status-Code", "value": "3000"}, {"name": "Via", "value": "metds-49-190974[200], cache4.cn6382[92,0]"}, {"name": "Timing-Allow-Origin", "value": "*"}, {"name": "EagleId", "value": "78e9b19817548927663771411e"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":200,\"code_v2\":200}", "size": 26, "mimeType": "text/plain"}}, "time": 115.99993705749512, "startedDateTime": "2025-08-11T14:12:46.082Z"}, {"request": {"method": "POST", "bodySize": 57, "headersSize": 933, "postData": {"params": [], "text": "{\"t\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\",\"e\":11,\"f\":0}", "mimeType": "application/json"}, "cookies": [], "headers": [{"name": "Host", "value": "trace.zzqz2024.com"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Content-Length", "value": "57"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMTYwMzM2MzkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NiIsImV4cCI6MTc1NDkwMjg0MDg0NCwiaWF0IjoxNzU0ODkyNzYwODQ0LCJrZXkiOiJiYjI2ZTk0YmUzM2MyNDRjZGQyN2NmNDg4ZTIzYmY2MiIsInVzZXJuYW1lIjoiMTYwMzM2MzkifQ.NIIr5AUXXOz3cBQONQXeA4Lvugl3Bg1eMzRO89_ngswhpLdaJ6uL_kha9Fq_ZcfczsbHSID0YOX1uGOO31pSCg"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://trace.zzqz2024.com/api/trace/data/event/saveOrUpdate"}, "timings": {"connect": -1, "send": 1.0001659393310547, "dns": -1, "ssl": -1, "wait": 50.99987983703613, "blocked": -1, "receive": 3.999948501586914}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:41 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Expires", "value": "0"}, {"name": "X-Frame-Options", "value": "DENY"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"20000\",\"message\":\"成功响应\",\"date\":1754892761203,\"data\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\"}", "size": 108, "mimeType": "application/json"}}, "time": 55.9999942779541, "startedDateTime": "2025-08-11T14:12:40.902Z"}, {"request": {"method": "POST", "bodySize": 3142, "headersSize": 932, "postData": {"params": [{"name": "{\"channelCode\":\"0ba8c5e5f1d60cf6658f6a266a6feb2b\",\"data\":\"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", "value": "\",\"iv\":\"1754892760752000\",\"t\":\"9714c95326204e16937291ce8601fa8b\",\"uuid\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\",\"traceId\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\"}"}], "text": "{\"channelCode\":\"0ba8c5e5f1d60cf6658f6a266a6feb2b\",\"data\":\"YZHPQBba8wyvJ/0zg3wk1CuI1hRMndof+AHBMDhmJRMSqOooIOSgV29QLIJs9hAw81TTQmRy6ZJDJjJceCXGNGRyEVO+vC4nc9ucW5AD2lYl2ggdklpiz3DG8GGJlvENBU+lwVBKaZWCuoWbAUbJRW3/8EA9LP2Xea6ee5weR8iW5Hmf/QqWGlVbV0ck1K6OcePZacY1ymYAj6nP3gxkUwj621ZPy/8YP6sTR7wkUDlZvACYFqYA+5nLAXTNRqTh7BgRY44dbwToCA5CjNBC55FFJrGROQfsEvIa9Pu9sBVDzy0csEAtfGqtInGuihAzhmppqdOGJipg77mcXIr+EXfj4ZzTHF65YuQxNPfTKB/E3tinAkZ6C+jKOf/E8Q/2/OqHmn5OWmUXxhtSyxOe+BXh/fbx+yF3SXYTySns8bzFS0ILn1Mfj8tocN1Mz7FS+4UtvlEtMA3P5Qc060X5CGMacYe2awc9eRPoSkOId3/OxI8nVnSro8B1TI0TS8mx3/2/y9FpksDtrSFhiAoF9Oq8Keg6dL9cnnNUEZDDaj9S01orsIuRgamUKTUPUvKjtAh51Kh7oFa4LQlPxMh3mLcP7dCXjzWubwJ6riXd3/RQr4lMakv+MLREjwEr7ISYX8e4l6ROVJ7lxt+4ok14IiDPgmuFHPOTFwTMwtZihqkU0dnFmpnLPbvij8gdBXnUNt0SrKCetWGKzvcLsa3pY5pkv6AQPTHEjGp/WU5FRCJ4awm1t+qt1f7B16oIHAqAyR08YJhVt239MJ7/XVkNOsOsm4nUhClFBQD7UGzD/T5yvSCsFhw4ZEKU/A7sJ038bKBxrdED+Cu9JoL5svyTyPeFsX/JA5obi6ez8FSUYBfKxVt6l+eb/qLvkr64+hs8vNyLEj2FvWVVsWzyVQYzTIuOyGoUD0EGBF++Ek4BTh+YawtGRY1BP86mqBHxoZXhOt0GfWvGJlXxEFLtjpxI8sZHgz7Wkx+Qt4U4IYSPjk7vNVPc3toplwhNdr6l70zFn/OgPQ671LEaDwHPeepwdvY2WUz8rJ7hRLp1j9jOfKkOYNtHI6S2/LxpAdud5xtKv8qZYGx/6c36cVeMrk18xksIv205xoHLluAWLqN3Ljj0Plvz2QcsG6gr5No8FQXnd0L/+yvvUgFTppJMQs7dt3wmsmdCkttnwdT9oG4Qvb5j8F5hH1qyPi2BjlzyvLB3szV3eaLa/cZOhWUbUFu3u4T34vGLXc41tVQZtKrMC6hlqgFv2jEb22fCKjjB4YZKq1dpfvlNogNZELLSyG0oih7Dh1x23HQmNFEii4KL5Iug0WlDG9SFIpjrT6hpZwzmuDm0llI3qY53xxP5OZ0nBXkwI2ZLjmWAU+JkVbZQNy7ldQFtLrMYkvcMMU5rgo+LBEt6TzDeNGlhNsuIeayLXESRA/FK+kjVPzO5YfxdixhwCPpbXqEnvIqJbNZg/8eUGDvPsyW+/Lk0lBxtYogsfoSboWllGqs2jQnW2+3eeF3aCKb1jN/m9pDyXpjLQZum+40CKrOX7KdOIFWx3zaEQbU5tnaXXC5muvEg52kR1YCBTjosdF+qsCIcAl43rQU1X6zxIRdLbT6mnwD/CnFO4HQUxMIZqLVC/NCsCurKnSVkOYaIEs6jA/BoZzwmlie7FNnHvV0sRCMJni2Rg57riXd9KcPQl2DLE9Tlc4FEq7PM41M1e17q3a/1GqO6cAdobix3LhAhB7GifWluxqZIZB+YobivAT1RvLY3bpGsfInHHAT8U0ovw8q3Y4rpLSquu4/Qzmm0+XgXmtGUmOx+SGrXkQ0eqR7oypDrllY5KNC3Ws3VRysHKTLU6o+DmoU4W83kE+C5upOAA0MJwXckOckIgb0PMJdoY3g8BpilXrg9SprmWfS+ORdBg2lUKA0VdOK43ZBGEzw40roHs6n2Gv0lHN0450m7ujpBM9BrnpRhwf7ondfN89e0kUJP/n9/A04lWLYjdd+B421P1G5xneC4YJVnD6o1xgTV3bka7NvGHIEnsf1gxRhyQjpgoRRnxwlzDb6D6aHCBm2ZHDTaymWb6nBcgP10NAujkSZPnQJUkXT7mtUYQI5IqzLL+hrpaAzXNjEwxjQm7f8y+xyvxcYMslhpNRRGn3v23i5PdImA0BccJAn+BUKVFfV85LmI2kiPrSMbajaIBG1ZResKLlKliM+qu0exPg9poQ/8o6EZzK8wqLsqWJy31DBW9Lqk2nQzHSTLHRpT8fkQ5pADRH+5HuECSAuGRTQRUXlp0JOC772t1C8QM5CuXtL8BXxBIDE7sFcmToS7NJC+9KjsX7DJWt24lxPu1g9gj5anhS0Q/93RThkoD0WECng7t/mZHACqa+nUzIa7tRkCIfLTUX/r/F8DIUpyVpQWFSQmBvyapFNvLCupWmnerFzScs696cR2CnnoDOSA13T5z2U6qRrSy/VRDJTLBb9kcoJdABD3sl8hnoNdlJuEQhlQtZmCb4rSfz9o1y/58cIsQkiDfs9mWb6tj+TTarnjXR4DlZpfzi2BOl+Vd4UKp+mZBGZolTTZ8czBL7VTaZLUHFfAt1TzSyljs/rqyIBLIbQ1b60SZW4DhnpADG9MbLcGFDTeE0YrNaL+fXRUsLTDeEKli04dy1qLvQtnx+tmmQHgXVf1VzOGlNFHruBlg0G1e7Sk45b/9lT8nNFLHb/MkyGu/hdN6J1pGaCOdD0gDKsHw1qCdeJFpdg9/NZEn3jd/aoHwoy6+LRLxh7BkcQIh85gVpuIXYv/mhzKewgKe2x5kdJ4i8yAiTUARtUITSl8erYiZJ2PSBEqjQsN8Y6JoP32GWIgtIsO0rVdgffHAiF6kVgNlOFz40GPPOg7MsXa9Yf6DdEEvKIoNq9vwtqz3Con7KnEtREmhwmHEYYp9IPcHAk=\",\"iv\":\"1754892760752000\",\"t\":\"9714c95326204e16937291ce8601fa8b\",\"uuid\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\",\"traceId\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\"}", "mimeType": "application/json"}, "cookies": [], "headers": [{"name": "Host", "value": "api.zzqz2024.com"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Content-Length", "value": "3142"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMTYwMzM2MzkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NiIsImV4cCI6MTc1NDkwMjg0MDg0NCwiaWF0IjoxNzU0ODkyNzYwODQ0LCJrZXkiOiJiYjI2ZTk0YmUzM2MyNDRjZGQyN2NmNDg4ZTIzYmY2MiIsInVzZXJuYW1lIjoiMTYwMzM2MzkifQ.NIIr5AUXXOz3cBQONQXeA4Lvugl3Bg1eMzRO89_ngswhpLdaJ6uL_kha9Fq_ZcfczsbHSID0YOX1uGOO31pSCg"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://api.zzqz2024.com/api/am/command/encrypt/signOrder"}, "timings": {"connect": -1, "send": 5.000114440917969, "dns": -1, "ssl": -1, "wait": 62.99996376037598, "blocked": -1, "receive": 3.999948501586914}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:41 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"20000\",\"message\":\"成功响应\",\"date\":1754892761123,\"data\":{\"reason\":0,\"encryptData\":\"5KE4QXgxcpAHhV0mm/USoIqzzj50xy1BrFa9DbO6MLBz0MngfE6Xoi0/t7Iq6daL0fNT3GTnbfrbfzrnmgokUesz4frMqeXr9rJRjOFKHC7AxtPQgvgePXXyHAgIxcDZrRyX9EPMwGGm9l9Ap2WOdoAmSR091nFxD2x5aJdq+qnuhMo9FVSLhk9hAP3D6/tXUPI9Jv+DNPiilydJZEoPyDW6WI/vJNS7qGgefxVggwYqLhEa8kTfBO0twtEPIj/HIWoUhJu3uqqCrPMUsMcFoPyRUsCsNg9x9/nfkcQMmlQ=\"}}", "size": 399, "mimeType": "application/json"}}, "time": 72.00002670288086, "startedDateTime": "2025-08-11T14:12:40.820Z"}, {"request": {"method": "OPTIONS", "bodySize": 0, "headersSize": 607, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "api.zzqz2024.com"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Access-Control-Request-Method", "value": "POST"}, {"name": "Access-Control-Request-Headers", "value": "authorization,content-type"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Content-Length", "value": "0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://api.zzqz2024.com/api/am/command/encrypt/signOrder"}, "timings": {"connect": -1, "send": 0, "dns": -1, "ssl": -1, "wait": 49.99995231628418, "blocked": -1, "receive": 5.000114440917969}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:41 GMT"}, {"name": "Content-Length", "value": "0"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Methods", "value": "POST"}, {"name": "Access-Control-Allow-Headers", "value": "authorization, content-type"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "httpVersion": "HTTP/1.1", "content": {"size": 0, "text": ""}}, "time": 55.00006675720215, "startedDateTime": "2025-08-11T14:12:40.757Z"}, {"request": {"method": "GET", "bodySize": 0, "headersSize": 958, "postData": {"params": [], "text": "", "mimeType": "application/json"}, "cookies": [], "headers": [{"name": "Host", "value": "api.zzqz2024.com"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMTYwMzM2MzkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NiIsImV4cCI6MTc1NDkwMjg0MDg0NCwiaWF0IjoxNzU0ODkyNzYwODQ0LCJrZXkiOiJiYjI2ZTk0YmUzM2MyNDRjZGQyN2NmNDg4ZTIzYmY2MiIsInVzZXJuYW1lIjoiMTYwMzM2MzkifQ.NIIr5AUXXOz3cBQONQXeA4Lvugl3Bg1eMzRO89_ngswhpLdaJ6uL_kha9Fq_ZcfczsbHSID0YOX1uGOO31pSCg"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [{"name": "channelCode", "value": "0ba8c5e5f1d60cf6658f6a266a6feb2b"}], "httpVersion": "HTTP/1.1", "url": "https://api.zzqz2024.com/api/channel/getChannelEncryptionInfo?channelCode=0ba8c5e5f1d60cf6658f6a266a6feb2b"}, "timings": {"connect": -1, "send": 0, "dns": -1, "ssl": -1, "wait": 46.99993133544922, "blocked": -1, "receive": 3.999948501586914}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"20000\",\"message\":\"成功响应\",\"date\":1754892760978,\"data\":\"YhIg0qho2Lp0JfjsebY6QMclYoV5eQYXfmN49oKmnGYCKlLZmFTtOEuGyz+vLQPhWh2g4MprE4P0+qOSLrulUO4DTdT9l/ISN3i2qNiOPYpa0X/5knyI9mRd3aOzdV91ZwlepAbv+hccuoERz7cI1N5sVmlSAfUb1PC7ltoV8AU=\"}", "size": 244, "mimeType": "application/json"}}, "time": 50.99987983703613, "startedDateTime": "2025-08-11T14:12:40.680Z"}, {"request": {"method": "OPTIONS", "bodySize": 0, "headersSize": 655, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "api.zzqz2024.com"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Access-Control-Request-Method", "value": "GET"}, {"name": "Access-Control-Request-Headers", "value": "authorization,content-type"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Content-Length", "value": "0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [{"name": "channelCode", "value": "0ba8c5e5f1d60cf6658f6a266a6feb2b"}], "httpVersion": "HTTP/1.1", "url": "https://api.zzqz2024.com/api/channel/getChannelEncryptionInfo?channelCode=0ba8c5e5f1d60cf6658f6a266a6feb2b"}, "timings": {"connect": -1, "send": 0, "dns": -1, "ssl": -1, "wait": 60.00018119812012, "blocked": -1, "receive": 3.999948501586914}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "Content-Length", "value": "0"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Methods", "value": "GET"}, {"name": "Access-Control-Allow-Headers", "value": "authorization, content-type"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "httpVersion": "HTTP/1.1", "content": {"size": 0, "text": ""}}, "time": 64.00012969970703, "startedDateTime": "2025-08-11T14:12:40.610Z"}, {"request": {"method": "POST", "bodySize": 24, "headersSize": 919, "postData": {"params": [], "text": "{\"mobile\":\"13802913946\"}", "mimeType": "application/json"}, "cookies": [], "headers": [{"name": "Host", "value": "api.zzqz2024.com"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Content-Length", "value": "24"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMjE4MTQ4OTkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NCIsImV4cCI6MTc1MjczNDM2MjE2OSwiaWF0IjoxNzUyNzI0MjgyMTY5LCJrZXkiOiJlOTEwNWEzZjdhYmJjODFkMGI4MTE1MmY3ZGE2OTUzZCIsInVzZXJuYW1lIjoiMjE4MTQ4OTkifQ.IlSabR55JzWpwwobs4VMI6C38P_HplWYBNtGl1A1oxs_hbK534pE0YkPnimajtBdfOUtmflqqwQDJXIH8IynXQ"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://api.zzqz2024.com/api/app/auth/register"}, "timings": {"connect": -1, "send": 3.000020980834961, "dns": -1, "ssl": -1, "wait": 154.9999713897705, "blocked": -1, "receive": 3.999948501586914}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"20000\",\"message\":\"成功响应\",\"date\":1754892760846,\"data\":{\"username\":\"13802913946\",\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMTYwMzM2MzkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NiIsImV4cCI6MTc1NDkwMjg0MDg0NCwiaWF0IjoxNzU0ODkyNzYwODQ0LCJrZXkiOiJiYjI2ZTk0YmUzM2MyNDRjZGQyN2NmNDg4ZTIzYmY2MiIsInVzZXJuYW1lIjoiMTYwMzM2MzkifQ.NIIr5AUXXOz3cBQONQXeA4Lvugl3Bg1eMzRO89_ngswhpLdaJ6uL_kha9Fq_ZcfczsbHSID0YOX1uGOO31pSCg\",\"refreshToken\":null,\"needAuthorizeAlipayOpenid\":null,\"needAuthorizeWechatOpenid\":null,\"uselessField\":null,\"productList\":null}}", "size": 604, "mimeType": "application/json"}}, "time": 161.99994087219238, "startedDateTime": "2025-08-11T14:12:40.440Z"}, {"request": {"method": "OPTIONS", "bodySize": 0, "headersSize": 596, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "api.zzqz2024.com"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Access-Control-Request-Method", "value": "POST"}, {"name": "Access-Control-Request-Headers", "value": "authorization,content-type"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Content-Length", "value": "0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://api.zzqz2024.com/api/app/auth/register"}, "timings": {"connect": -1, "send": 0, "dns": -1, "ssl": -1, "wait": 54.99982833862305, "blocked": -1, "receive": 6.000041961669922}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "Content-Length", "value": "0"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Methods", "value": "POST"}, {"name": "Access-Control-Allow-Headers", "value": "authorization, content-type"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "httpVersion": "HTTP/1.1", "content": {"size": 0, "text": ""}}, "time": 60.99987030029297, "startedDateTime": "2025-08-11T14:12:40.374Z"}, {"request": {"method": "POST", "bodySize": 57, "headersSize": 933, "postData": {"params": [], "text": "{\"t\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\",\"e\":10,\"f\":0}", "mimeType": "application/json"}, "cookies": [], "headers": [{"name": "Host", "value": "trace.zzqz2024.com"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Content-Length", "value": "57"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMjE4MTQ4OTkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NCIsImV4cCI6MTc1MjczNDM2MjE2OSwiaWF0IjoxNzUyNzI0MjgyMTY5LCJrZXkiOiJlOTEwNWEzZjdhYmJjODFkMGI4MTE1MmY3ZGE2OTUzZCIsInVzZXJuYW1lIjoiMjE4MTQ4OTkifQ.IlSabR55JzWpwwobs4VMI6C38P_HplWYBNtGl1A1oxs_hbK534pE0YkPnimajtBdfOUtmflqqwQDJXIH8IynXQ"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://trace.zzqz2024.com/api/trace/data/event/saveOrUpdate"}, "timings": {"connect": -1, "send": 4.999876022338867, "dns": -1, "ssl": -1, "wait": 134.99999046325684, "blocked": -1, "receive": 5.000114440917969}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Expires", "value": "0"}, {"name": "X-Frame-Options", "value": "DENY"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"20000\",\"message\":\"成功响应\",\"date\":1754892760757,\"data\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\"}", "size": 108, "mimeType": "application/json"}}, "time": 144.99998092651367, "startedDateTime": "2025-08-11T14:12:40.367Z"}, {"request": {"method": "POST", "bodySize": 191, "headersSize": 927, "postData": {"params": [], "text": "{\"channelCode\":\"0ba8c5e5f1d60cf6658f6a266a6feb2b\",\"productCode\":\"LH2565\",\"uuid\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\",\"mobile\":\"13802913946\",\"traceId\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\"}", "mimeType": "application/json"}, "cookies": [], "headers": [{"name": "Host", "value": "api.zzqz2024.com"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Content-Length", "value": "191"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMjE4MTQ4OTkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NCIsImV4cCI6MTc1MjczNDM2MjE2OSwiaWF0IjoxNzUyNzI0MjgyMTY5LCJrZXkiOiJlOTEwNWEzZjdhYmJjODFkMGI4MTE1MmY3ZGE2OTUzZCIsInVzZXJuYW1lIjoiMjE4MTQ4OTkifQ.IlSabR55JzWpwwobs4VMI6C38P_HplWYBNtGl1A1oxs_hbK534pE0YkPnimajtBdfOUtmflqqwQDJXIH8IynXQ"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://api.zzqz2024.com/api/am/command/generateToken"}, "timings": {"connect": -1, "send": 4.000186920166016, "dns": -1, "ssl": -1, "wait": 278.99980545043945, "blocked": -1, "receive": 6.000041961669922}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"20000\",\"message\":\"成功响应\",\"date\":1754892760457,\"data\":{\"reason\":0,\"t\":\"KdE110dWyZmy3rJRzt37JpBAv8tYPNtjkM/4WN5+F1BdSCwbicMfWnJFMOxTDYnn\",\"i\":\"1754892760594\"}}", "size": 173, "mimeType": "application/json"}}, "time": 289.0000343322754, "startedDateTime": "2025-08-11T14:12:40.067Z"}, {"request": {"method": "OPTIONS", "bodySize": 0, "headersSize": 603, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "api.zzqz2024.com"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Access-Control-Request-Method", "value": "POST"}, {"name": "Access-Control-Request-Headers", "value": "authorization,content-type"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Content-Length", "value": "0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://api.zzqz2024.com/api/am/command/generateToken"}, "timings": {"connect": 290.00020027160645, "send": 230.99994659423828, "dns": -1, "ssl": 226.99999809265137, "wait": 46.99993133544922, "blocked": -1, "receive": 3.000020980834961}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "Content-Length", "value": "0"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Methods", "value": "POST"}, {"name": "Access-Control-Allow-Headers", "value": "authorization, content-type"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "httpVersion": "HTTP/1.1", "content": {"size": 0, "text": ""}}, "time": 49.99995231628418, "startedDateTime": "2025-08-11T14:12:40.009Z"}, {"request": {"method": "POST", "bodySize": 50, "headersSize": 933, "postData": {"params": [], "text": "{\"t\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\",\"e\":9}", "mimeType": "application/json"}, "cookies": [], "headers": [{"name": "Host", "value": "trace.zzqz2024.com"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Content-Length", "value": "50"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMjE4MTQ4OTkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NCIsImV4cCI6MTc1MjczNDM2MjE2OSwiaWF0IjoxNzUyNzI0MjgyMTY5LCJrZXkiOiJlOTEwNWEzZjdhYmJjODFkMGI4MTE1MmY3ZGE2OTUzZCIsInVzZXJuYW1lIjoiMjE4MTQ4OTkifQ.IlSabR55JzWpwwobs4VMI6C38P_HplWYBNtGl1A1oxs_hbK534pE0YkPnimajtBdfOUtmflqqwQDJXIH8IynXQ"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://trace.zzqz2024.com/api/trace/data/event/saveOrUpdate"}, "timings": {"connect": 246.99997901916504, "send": 204.9999237060547, "dns": -1, "ssl": 198.99988174438477, "wait": 131.99996948242188, "blocked": -1, "receive": 4.999876022338867}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Expires", "value": "0"}, {"name": "X-Frame-Options", "value": "DENY"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"20000\",\"message\":\"成功响应\",\"date\":1754892760336,\"data\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\"}", "size": 108, "mimeType": "application/json"}}, "time": 139.9998664855957, "startedDateTime": "2025-08-11T14:12:39.957Z"}, {"request": {"method": "GET", "bodySize": 0, "headersSize": 2509, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "zlsj-liuliangguo-log.cn-hangzhou.log.aliyuncs.com"}, {"name": "Sec-Fetch-Dest", "value": "image"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "Accept", "value": "image/webp,image/avif,image/jxl,image/heic,image/heic-sequence,video/*;q=0.8,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Mode", "value": "no-cors"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=5, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [{"name": "APIVersion", "value": "0.6.0"}, {"name": "type", "value": "liuliangguoSDK"}, {"name": "url", "value": "https%3A%2F%2Fh5.syhy123.com%2Faqy_msv2%2F%3Fa%3D0ba8c5e5f1d60cf6658f6a266a6feb2b%26projectid%3D7526720476899622948%26promotionid%3D7526720211249496127%26creativetype%3D5%26clickid%3DEInpqLrQ6KEDGJ_e-anygLAGIJ_e-anygLAGMA44-6D7ygNCKDhlZTI0YmYwLTRmYjMtNGMzYi1iMzE5LWEyOTE3MDI0YTAxNnUxMTJIgNKTrQOQAQA%26ad_id%3D1837580368828619%26_toutiao_params%3D%257B%252522cid%252522%3A1837580405060745%2C%252522device_id%252522%3A3588836642680607%2C%252522log_extra%252522%3A%252522%257B%255C%252522ad_price%255C%252522%3A%255C%252522aHXtZAAL81Zode1kAAvzVlbRf-bJNgkVsAN8PQ%255C%252522%2C%255C%252522city_id%255C%252522%3Anull%2C%255C%252522convert_id%255C%252522%3A0%2C%255C%252522country_id%255C%252522%3Anull%2C%255C%252522dma_id%255C%252522%3Anull%2C%255C%252522orit%255C%252522%3A900000000%2C%255C%252522province_id%255C%252522%3Anull%2C%255C%252522req_id%255C%252522%3A%255C%2525228ee24bf0-4fb3-4c3b-b319-a2917024a016u112%255C%252522%2C%255C%252522rit%255C%252522%3A962515067%257D%252522%2C%252522orit%252522%3A900000000%2C%252522req_id%252522%3A%2525228ee24bf0-4fb3-4c3b-b319-a2917024a016u112%252522%2C%252522rit%252522%3A962515067%2C%252522sign%252522%3A%252522D41D8CD98F00B204E9800998ECF8427E%252522%2C%252522uid%252522%3A3588836642680607%2C%252522ut%252522%3A14%257D"}, {"name": "time", "value": "2025-08-11%2014%3A12%3A39"}, {"name": "diff_time", "value": "14270"}, {"name": "method", "value": "sdkClick"}, {"name": "uuid", "value": "3b25b1c3c65f41498cb14e7edbce230f"}, {"name": "browser_unique_id", "value": "9818f22c7d34a487f3bababe554e965b"}, {"name": "ua", "value": "Mozilla%2F5.0%20(iPhone%3B%20CPU%20iPhone%20OS%2018_6%20like%20Mac%20OS%20X)%20AppleWebKit%2F605.1.15%20(KHTML%2C%20like%20Gecko)%20Version%2F18.6%20Mobile%2F15E148%20Safari%2F604.1"}, {"name": "version", "value": "1.3.2"}, {"name": "app_key", "value": ""}, {"name": "app_secret", "value": ""}, {"name": "phone", "value": ""}, {"name": "product_name", "value": ""}, {"name": "template_name", "value": ""}, {"name": "pid", "value": ""}, {"name": "SN", "value": "7"}, {"name": "llgtid", "value": "2b9580857ad24f71aaef2ebb6909b167"}, {"name": "html", "value": "%3Cuni-view%20data-v-7e66ffd6%3D%22%22%20class%3D%22submit-btn%22%3E%E5%90%8C%E6%84%8F%E5%B9%B6%E7%BB%A7%E7%BB%AD%3C%2Funi-view%3E"}], "httpVersion": "HTTP/1.1", "url": "https://zlsj-liuliangguo-log.cn-hangzhou.log.aliyuncs.com/logstores/llg-sdk-logs/track.gif?APIVersion=0.6.0&type=liuliangguoSDK&url=https%3A%2F%2Fh5.syhy123.com%2Faqy_msv2%2F%3Fa%3D0ba8c5e5f1d60cf6658f6a266a6feb2b%26projectid%3D7526720476899622948%26promotionid%3D7526720211249496127%26creativetype%3D5%26clickid%3DEInpqLrQ6KEDGJ_e-anygLAGIJ_e-anygLAGMA44-6D7ygNCKDhlZTI0YmYwLTRmYjMtNGMzYi1iMzE5LWEyOTE3MDI0YTAxNnUxMTJIgNKTrQOQAQA%26ad_id%3D1837580368828619%26_toutiao_params%3D%257B%252522cid%252522%3A1837580405060745%2C%252522device_id%252522%3A3588836642680607%2C%252522log_extra%252522%3A%252522%257B%255C%252522ad_price%255C%252522%3A%255C%252522aHXtZAAL81Zode1kAAvzVlbRf-bJNgkVsAN8PQ%255C%252522%2C%255C%252522city_id%255C%252522%3Anull%2C%255C%252522convert_id%255C%252522%3A0%2C%255C%252522country_id%255C%252522%3Anull%2C%255C%252522dma_id%255C%252522%3Anull%2C%255C%252522orit%255C%252522%3A900000000%2C%255C%252522province_id%255C%252522%3Anull%2C%255C%252522req_id%255C%252522%3A%255C%2525228ee24bf0-4fb3-4c3b-b319-a2917024a016u112%255C%252522%2C%255C%252522rit%255C%252522%3A962515067%257D%252522%2C%252522orit%252522%3A900000000%2C%252522req_id%252522%3A%2525228ee24bf0-4fb3-4c3b-b319-a2917024a016u112%252522%2C%252522rit%252522%3A962515067%2C%252522sign%252522%3A%252522D41D8CD98F00B204E9800998ECF8427E%252522%2C%252522uid%252522%3A3588836642680607%2C%252522ut%252522%3A14%257D&time=2025-08-11%2014%3A12%3A39&diff_time=14270&method=sdkClick&uuid=3b25b1c3c65f41498cb14e7edbce230f&browser_unique_id=9818f22c7d34a487f3bababe554e965b&ua=Mozilla%2F5.0%20(iPhone%3B%20CPU%20iPhone%20OS%2018_6%20like%20Mac%20OS%20X)%20AppleWebKit%2F605.1.15%20(KHTML%2C%20like%20Gecko)%20Version%2F18.6%20Mobile%2F15E148%20Safari%2F604.1&version=1.3.2&app_key=&app_secret=&phone=&product_name=&template_name=&pid=&SN=7&llgtid=2b9580857ad24f71aaef2ebb6909b167&html=%3Cuni-view%20data-v-7e66ffd6%3D%22%22%20class%3D%22submit-btn%22%3E%E5%90%8C%E6%84%8F%E5%B9%B6%E7%BB%A7%E7%BB%AD%3C%2Funi-view%3E"}, "timings": {"connect": 284.9998474121094, "send": 219.00010108947754, "dns": -1, "ssl": 203.99999618530273, "wait": 34.999847412109375, "blocked": -1, "receive": 3.000020980834961}, "serverIPAddress": "************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Server", "value": "AliyunSLS"}, {"name": "Content-Type", "value": "image/gif"}, {"name": "Content-Length", "value": "43"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:40 GMT"}, {"name": "x-log-time", "value": "1754892760"}, {"name": "x-log-requestid", "value": "689989D7FACC2FA2CD70E76A"}], "httpVersion": "HTTP/1.1", "content": {"size": 43, "mimeType": "image/gif", "encoding": "base64", "text": "R0lGODlhAQABAIABAAAAAP///yH5BAEAAAEALAAAAAABAAEAAAICTAEAOw=="}}, "time": 37.999868392944336, "startedDateTime": "2025-08-11T14:12:39.710Z"}, {"request": {"method": "POST", "bodySize": 50, "headersSize": 933, "postData": {"params": [], "text": "{\"t\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\",\"e\":4}", "mimeType": "application/json"}, "cookies": [], "headers": [{"name": "Host", "value": "trace.zzqz2024.com"}, {"name": "Accept", "value": "*/*"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Content-Length", "value": "50"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiMjE4MTQ4OTkiLCJzb3VyY2VUeXBlIjoiMiIsImlzcyI6InFiX3RlY2giLCJtb2JpbGUiOiIxMzgwMjkxMzk0NCIsImV4cCI6MTc1MjczNDM2MjE2OSwiaWF0IjoxNzUyNzI0MjgyMTY5LCJrZXkiOiJlOTEwNWEzZjdhYmJjODFkMGI4MTE1MmY3ZGE2OTUzZCIsInVzZXJuYW1lIjoiMjE4MTQ4OTkifQ.IlSabR55JzWpwwobs4VMI6C38P_HplWYBNtGl1A1oxs_hbK534pE0YkPnimajtBdfOUtmflqqwQDJXIH8IynXQ"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://trace.zzqz2024.com/api/trace/data/event/saveOrUpdate"}, "timings": {"connect": -1, "send": 0.9999275207519531, "dns": -1, "ssl": -1, "wait": 42.00005531311035, "blocked": -1, "receive": 1.9998550415039062}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:38 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Expires", "value": "0"}, {"name": "X-Frame-Options", "value": "DENY"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":\"20000\",\"message\":\"成功响应\",\"date\":1754892758237,\"data\":\"1c269a7b-fa44-475f-adeb-3845bb1e3803\"}", "size": 108, "mimeType": "application/json"}}, "time": 44.99983787536621, "startedDateTime": "2025-08-11T14:12:37.943Z"}, {"request": {"method": "OPTIONS", "bodySize": 0, "headersSize": 610, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "trace.zzqz2024.com"}, {"name": "Origin", "value": "https://h5.syhy123.com"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Access-Control-Request-Method", "value": "POST"}, {"name": "Access-Control-Request-Headers", "value": "authorization,content-type"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Content-Length", "value": "0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=3, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://trace.zzqz2024.com/api/trace/data/event/saveOrUpdate"}, "timings": {"connect": 363.9998435974121, "send": 282.99999237060547, "dns": -1, "ssl": 277.9998779296875, "wait": 42.00005531311035, "blocked": -1, "receive": 3.000020980834961}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "", "cookies": [], "headers": [{"name": "Server", "value": "nginx/1.24.0"}, {"name": "Date", "value": "Mon, 11 Aug 2025 06:12:38 GMT"}, {"name": "Content-Length", "value": "0"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Access-Control-Allow-Origin", "value": "https://h5.syhy123.com"}, {"name": "Access-Control-Allow-Methods", "value": "POST"}, {"name": "Access-Control-Allow-Headers", "value": "authorization, content-type"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Expires", "value": "0"}, {"name": "X-Frame-Options", "value": "DENY"}], "httpVersion": "HTTP/1.1", "content": {"size": 0, "text": ""}}, "time": 45.00007629394531, "startedDateTime": "2025-08-11T14:12:37.891Z"}, {"request": {"method": "GET", "bodySize": 0, "headersSize": 649, "postData": {"text": "", "params": []}, "cookies": [], "headers": [{"name": "Host", "value": "web.78keji.cn"}, {"name": "Accept", "value": "image/webp,image/avif,image/jxl,image/heic,image/heic-sequence,video/*;q=0.8,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "If-None-Match", "value": "\"67875def-3053\""}, {"name": "Sec-Fetch-Mode", "value": "no-cors"}, {"name": "If-Modified-Since", "value": "Wed, 15 Jan 2025 07:04:15 GMT"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://h5.syhy123.com/"}, {"name": "Sec-Fetch-Dest", "value": "image"}, {"name": "Accept-Language", "value": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9"}, {"name": "Priority", "value": "u=5, i"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://web.78keji.cn/v2img/aiqiyi/ms/tow.gif"}, "timings": {"connect": 322.9999542236328, "send": 156.00013732910156, "dns": -1, "ssl": 153.0001163482666, "wait": 24.99985694885254, "blocked": -1, "receive": 3.999948501586914}, "serverIPAddress": "***************", "response": {"status": 304, "cookies": [], "content": {"text": "", "size": 0, "mimeType": "image/gif"}, "headers": [{"name": "Etag", "value": "\"67875def-3053\""}, {"name": "Server", "value": "nginx"}, {"name": "Date", "value": "Mon, 12 May 2025 10:34:08 GMT"}, {"name": "Content-Type", "value": "image/gif"}, {"name": "Expires", "value": "Wed, 11 Jun 2025 10:34:08 GMT"}, {"name": "Strict-Transport-Security", "value": "max-age=31536000"}, {"name": "Cache-Control", "value": "max-age=2592000"}, {"name": "X-NWS-LOG-UUID", "value": "16730968927675949438"}, {"name": "Connection", "value": "close"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "httpVersion": "HTTP/1.1"}, "time": 28.999805450439453, "startedDateTime": "2025-08-11T14:12:37.854Z"}], "pages": [], "creator": {"name": "Stream", "version": "1.0.6"}, "version": "1.2"}}