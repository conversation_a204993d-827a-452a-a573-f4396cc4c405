<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付流程接口监控器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            line-height: 1.4;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #00ff00;
        }
        
        .monitor-section {
            background: #2d2d2d;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .section-header {
            background: #3d3d3d;
            padding: 15px 20px;
            border-bottom: 1px solid #4d4d4d;
            font-weight: bold;
            color: #00ffff;
        }
        
        .section-content {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 15px;
            padding: 10px;
            background: #1a1a1a;
            border-radius: 4px;
            border-left: 3px solid #00ff00;
        }
        
        .log-timestamp {
            color: #ffff00;
            font-size: 12px;
        }
        
        .log-method {
            color: #ff6600;
            font-weight: bold;
        }
        
        .log-url {
            color: #00ffff;
            word-break: break-all;
        }
        
        .log-headers, .log-body {
            margin-top: 10px;
            padding: 10px;
            background: #0d1117;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .log-response {
            color: #90ee90;
        }
        
        .control-panel {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .control-btn {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .control-btn:hover {
            background: #00cc00;
        }
        
        .control-btn.stop {
            background: #ff4444;
            color: white;
        }
        
        .control-btn.stop:hover {
            background: #cc0000;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: #1a1a1a;
            padding: 10px 15px;
            border-radius: 4px;
            border-left: 3px solid #00ffff;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #00ffff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #888;
        }
        
        .iframe-container {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #4d4d4d;
            border-radius: 4px;
            background: white;
        }
        
        .json-viewer {
            background: #0d1117;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .highlight {
            background: #ffff0033;
            padding: 2px 4px;
            border-radius: 2px;
        }
        
        .error {
            color: #ff4444;
        }
        
        .success {
            color: #00ff00;
        }
        
        .warning {
            color: #ffaa00;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 支付流程接口监控器</h1>
            <p>实时监控爱奇艺支付页面的所有网络请求</p>
        </div>
        
        <div class="control-panel">
            <button class="control-btn" onclick="startMonitoring()">🚀 开始监控</button>
            <button class="control-btn stop" onclick="stopMonitoring()">⏹️ 停止监控</button>
            <button class="control-btn" onclick="clearLogs()">🗑️ 清空日志</button>
            <button class="control-btn" onclick="exportLogs()">📥 导出日志</button>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalRequests">0</div>
                    <div class="stat-label">总请求数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="apiRequests">0</div>
                    <div class="stat-label">API请求</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="paymentRequests">0</div>
                    <div class="stat-label">支付相关</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="alipayRequests">0</div>
                    <div class="stat-label">支付宝调用</div>
                </div>
            </div>
        </div>
        
        <div class="iframe-container">
            <div class="section-header">📱 测试页面</div>
            <iframe id="testFrame" class="test-iframe" src="about:blank"></iframe>
        </div>
        
        <div class="monitor-section">
            <div class="section-header">🌐 网络请求监控</div>
            <div class="section-content" id="networkLogs"></div>
        </div>
        
        <div class="monitor-section">
            <div class="section-header">💰 支付相关请求</div>
            <div class="section-content" id="paymentLogs"></div>
        </div>
        
        <div class="monitor-section">
            <div class="section-header">🔗 支付宝跳转监控</div>
            <div class="section-content" id="alipayLogs"></div>
        </div>
        
        <div class="monitor-section">
            <div class="section-header">📊 完整流程分析</div>
            <div class="section-content" id="flowAnalysis"></div>
        </div>
    </div>

    <script>
        let isMonitoring = false;
        let requestCount = 0;
        let apiCount = 0;
        let paymentCount = 0;
        let alipayCount = 0;
        let allRequests = [];
        let originalFetch = window.fetch;
        let originalXHR = window.XMLHttpRequest;

        // 监控目标URL
        const targetUrl = 'https://h5.syhy123.com/aqy_msv2/?a=0ba8c5e5f1d60cf6658f6a266a6feb2b&projectid=7526720476899622948&promotionid=7526720211249496127&creativetype=5&phone=13802913949&product=vip_month&uuid=b508528a-5920-4b69-aac4-d89ae25e925c&t=1754893394520&source=generator';

        // 开始监控
        function startMonitoring() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            log('系统', '开始监控支付流程...', 'success');
            
            // 加载目标页面
            const iframe = document.getElementById('testFrame');
            iframe.src = targetUrl;
            
            // 监控iframe内的网络请求
            iframe.onload = function() {
                try {
                    setupIframeMonitoring(iframe);
                } catch (e) {
                    log('错误', '无法监控跨域iframe: ' + e.message, 'error');
                    // 使用代理方式监控
                    setupProxyMonitoring();
                }
            };
            
            // 监控当前页面的网络请求
            setupNetworkMonitoring();
        }

        // 停止监控
        function stopMonitoring() {
            isMonitoring = false;
            log('系统', '停止监控', 'warning');
            
            // 恢复原始方法
            window.fetch = originalFetch;
            window.XMLHttpRequest = originalXHR;
        }

        // 设置网络监控
        function setupNetworkMonitoring() {
            // 监控fetch请求
            window.fetch = function(...args) {
                if (isMonitoring) {
                    interceptRequest('fetch', args[0], args[1]);
                }
                return originalFetch.apply(this, args).then(response => {
                    if (isMonitoring) {
                        handleResponse('fetch', args[0], response.clone());
                    }
                    return response;
                });
            };

            // 监控XMLHttpRequest
            const originalOpen = XMLHttpRequest.prototype.open;
            const originalSend = XMLHttpRequest.prototype.send;
            
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._method = method;
                this._url = url;
                return originalOpen.apply(this, [method, url, ...args]);
            };
            
            XMLHttpRequest.prototype.send = function(data) {
                if (isMonitoring) {
                    interceptRequest('xhr', this._url, {
                        method: this._method,
                        body: data
                    });
                    
                    this.addEventListener('load', () => {
                        if (isMonitoring) {
                            handleResponse('xhr', this._url, {
                                status: this.status,
                                responseText: this.responseText,
                                getAllResponseHeaders: () => this.getAllResponseHeaders()
                            });
                        }
                    });
                }
                return originalSend.apply(this, arguments);
            };
        }

        // 设置iframe监控
        function setupIframeMonitoring(iframe) {
            const iframeWindow = iframe.contentWindow;
            const iframeDocument = iframe.contentDocument;
            
            if (!iframeWindow || !iframeDocument) {
                throw new Error('无法访问iframe内容');
            }
            
            // 监控iframe内的网络请求
            const iframeFetch = iframeWindow.fetch;
            iframeWindow.fetch = function(...args) {
                if (isMonitoring) {
                    interceptRequest('iframe-fetch', args[0], args[1]);
                }
                return iframeFetch.apply(this, args).then(response => {
                    if (isMonitoring) {
                        handleResponse('iframe-fetch', args[0], response.clone());
                    }
                    return response;
                });
            };
            
            // 监控页面跳转
            const originalLocation = iframeWindow.location;
            Object.defineProperty(iframeWindow, 'location', {
                set: function(value) {
                    if (isMonitoring) {
                        log('页面跳转', `跳转到: ${value}`, 'warning');
                        checkAlipayRedirect(value);
                    }
                    originalLocation.href = value;
                },
                get: function() {
                    return originalLocation;
                }
            });
            
            // 监控window.open
            const originalOpen = iframeWindow.open;
            iframeWindow.open = function(url, ...args) {
                if (isMonitoring) {
                    log('新窗口', `打开: ${url}`, 'warning');
                    checkAlipayRedirect(url);
                }
                return originalOpen.apply(this, [url, ...args]);
            };
        }

        // 设置代理监控
        function setupProxyMonitoring() {
            log('系统', '使用代理模式监控网络请求', 'warning');
            
            // 监控所有可能的支付相关域名
            const paymentDomains = [
                'api.zzqz2024.com',
                'trace.zzqz2024.com',
                'h5.syhy123.com',
                'alipay.com',
                'alipays.com'
            ];
            
            // 定期检查网络活动
            setInterval(() => {
                if (isMonitoring) {
                    checkNetworkActivity();
                }
            }, 1000);
        }

        // 拦截请求
        function interceptRequest(type, url, options = {}) {
            requestCount++;
            updateStats();
            
            const timestamp = new Date().toISOString();
            const method = options.method || 'GET';
            
            // 检查是否是API请求
            if (isApiRequest(url)) {
                apiCount++;
                updateStats();
            }
            
            // 检查是否是支付相关请求
            if (isPaymentRequest(url)) {
                paymentCount++;
                updateStats();
                logPaymentRequest(timestamp, type, method, url, options);
            }
            
            // 检查是否是支付宝相关
            if (isAlipayRequest(url)) {
                alipayCount++;
                updateStats();
                logAlipayRequest(timestamp, type, method, url, options);
            }
            
            // 记录所有请求
            const requestData = {
                timestamp,
                type,
                method,
                url,
                options,
                headers: options.headers || {}
            };
            
            allRequests.push(requestData);
            logNetworkRequest(requestData);
        }

        // 处理响应
        function handleResponse(type, url, response) {
            const timestamp = new Date().toISOString();
            
            if (isPaymentRequest(url)) {
                logPaymentResponse(timestamp, type, url, response);
            }
            
            if (isAlipayRequest(url)) {
                logAlipayResponse(timestamp, type, url, response);
            }
        }

        // 检查是否是API请求
        function isApiRequest(url) {
            return url.includes('api.') || url.includes('/api/');
        }

        // 检查是否是支付相关请求
        function isPaymentRequest(url) {
            const paymentKeywords = [
                'signOrder', 'createOrder', 'payment', 'pay', 'order',
                'zzqz2024.com', 'syhy123.com'
            ];
            return paymentKeywords.some(keyword => url.includes(keyword));
        }

        // 检查是否是支付宝相关
        function isAlipayRequest(url) {
            return url.includes('alipay') || url.includes('支付宝');
        }

        // 检查支付宝跳转
        function checkAlipayRedirect(url) {
            if (url.includes('alipay') || url.includes('支付宝')) {
                alipayCount++;
                updateStats();
                
                const timestamp = new Date().toISOString();
                logAlipayRedirect(timestamp, url);
                
                // 分析支付宝链接
                analyzeAlipayUrl(url);
            }
        }

        // 记录网络请求
        function logNetworkRequest(data) {
            const container = document.getElementById('networkLogs');
            const entry = createLogEntry(
                data.timestamp,
                `${data.type.toUpperCase()} ${data.method}`,
                data.url,
                data.options
            );
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        // 记录支付请求
        function logPaymentRequest(timestamp, type, method, url, options) {
            const container = document.getElementById('paymentLogs');
            const entry = createLogEntry(
                timestamp,
                `${type.toUpperCase()} ${method}`,
                url,
                options,
                'payment'
            );
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        // 记录支付响应
        function logPaymentResponse(timestamp, type, url, response) {
            const container = document.getElementById('paymentLogs');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <div class="log-timestamp">${timestamp}</div>
                <div class="log-method">RESPONSE</div>
                <div class="log-url">${url}</div>
                <div class="log-response">状态: ${response.status || 'N/A'}</div>
                <div class="json-viewer">${formatResponse(response)}</div>
            `;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        // 记录支付宝请求
        function logAlipayRequest(timestamp, type, method, url, options) {
            const container = document.getElementById('alipayLogs');
            const entry = createLogEntry(
                timestamp,
                `${type.toUpperCase()} ${method}`,
                url,
                options,
                'alipay'
            );
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        // 记录支付宝响应
        function logAlipayResponse(timestamp, type, url, response) {
            const container = document.getElementById('alipayLogs');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <div class="log-timestamp">${timestamp}</div>
                <div class="log-method">ALIPAY RESPONSE</div>
                <div class="log-url">${url}</div>
                <div class="json-viewer">${formatResponse(response)}</div>
            `;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        // 记录支付宝跳转
        function logAlipayRedirect(timestamp, url) {
            const container = document.getElementById('alipayLogs');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <div class="log-timestamp">${timestamp}</div>
                <div class="log-method success">🚀 支付宝跳转</div>
                <div class="log-url highlight">${url}</div>
                <div class="json-viewer">检测到支付宝应用调用</div>
            `;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        // 分析支付宝URL
        function analyzeAlipayUrl(url) {
            const container = document.getElementById('flowAnalysis');
            const analysis = document.createElement('div');
            analysis.className = 'log-entry';
            analysis.innerHTML = `
                <div class="log-timestamp">${new Date().toISOString()}</div>
                <div class="log-method success">💰 支付宝链接分析</div>
                <div class="json-viewer">${analyzeUrl(url)}</div>
            `;
            container.appendChild(analysis);
            container.scrollTop = container.scrollHeight;
        }

        // 创建日志条目
        function createLogEntry(timestamp, method, url, options = {}, type = 'normal') {
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            let className = '';
            if (type === 'payment') className = 'warning';
            if (type === 'alipay') className = 'success';
            
            entry.innerHTML = `
                <div class="log-timestamp">${timestamp}</div>
                <div class="log-method ${className}">${method}</div>
                <div class="log-url">${url}</div>
                ${options.headers ? `<div class="log-headers">Headers: ${JSON.stringify(options.headers, null, 2)}</div>` : ''}
                ${options.body ? `<div class="log-body">Body: ${formatBody(options.body)}</div>` : ''}
            `;
            
            return entry;
        }

        // 格式化响应
        function formatResponse(response) {
            if (response.responseText) {
                try {
                    const json = JSON.parse(response.responseText);
                    return JSON.stringify(json, null, 2);
                } catch (e) {
                    return response.responseText;
                }
            }
            return JSON.stringify(response, null, 2);
        }

        // 格式化请求体
        function formatBody(body) {
            if (typeof body === 'string') {
                try {
                    const json = JSON.parse(body);
                    return JSON.stringify(json, null, 2);
                } catch (e) {
                    return body;
                }
            }
            return JSON.stringify(body, null, 2);
        }

        // 分析URL
        function analyzeUrl(url) {
            const analysis = [];
            
            if (url.includes('alipay')) {
                analysis.push('✅ 确认为支付宝链接');
                
                // 提取参数
                try {
                    const urlObj = new URL(url);
                    const params = urlObj.searchParams;
                    
                    analysis.push('\n📋 URL参数:');
                    for (const [key, value] of params) {
                        analysis.push(`  ${key}: ${value}`);
                    }
                } catch (e) {
                    analysis.push('❌ URL解析失败');
                }
            }
            
            return analysis.join('\n');
        }

        // 更新统计
        function updateStats() {
            document.getElementById('totalRequests').textContent = requestCount;
            document.getElementById('apiRequests').textContent = apiCount;
            document.getElementById('paymentRequests').textContent = paymentCount;
            document.getElementById('alipayRequests').textContent = alipayCount;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('networkLogs').innerHTML = '';
            document.getElementById('paymentLogs').innerHTML = '';
            document.getElementById('alipayLogs').innerHTML = '';
            document.getElementById('flowAnalysis').innerHTML = '';
            
            requestCount = 0;
            apiCount = 0;
            paymentCount = 0;
            alipayCount = 0;
            allRequests = [];
            updateStats();
            
            log('系统', '日志已清空', 'success');
        }

        // 导出日志
        function exportLogs() {
            const data = {
                timestamp: new Date().toISOString(),
                stats: {
                    totalRequests: requestCount,
                    apiRequests: apiCount,
                    paymentRequests: paymentCount,
                    alipayRequests: alipayCount
                },
                requests: allRequests
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `payment-flow-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            log('系统', '日志已导出', 'success');
        }

        // 通用日志函数
        function log(type, message, level = 'normal') {
            console.log(`[${type}] ${message}`);
        }

        // 检查网络活动
        function checkNetworkActivity() {
            // 这里可以添加更多的网络活动检查逻辑
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('系统', '接口监控器已就绪', 'success');
        });
    </script>
</body>
</html>
