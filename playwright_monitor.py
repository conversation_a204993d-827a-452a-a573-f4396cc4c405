#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Playwright的支付流程监控器
使用Playwright MCP进行网络请求监控
"""

import json
import asyncio
import time
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from playwright.async_api import async_playwright

class PlaywrightPaymentMonitor:
    def __init__(self):
        self.requests_log = []
        self.responses_log = []
        self.payment_requests = []
        self.alipay_requests = []
        self.page = None
        self.browser = None
        self.context = None
        
        # 目标URL
        self.target_url = "https://h5.syhy123.com/aqy_msv2/?a=0ba8c5e5f1d60cf6658f6a266a6feb2b&projectid=7526720476899622948&promotionid=7526720211249496127&creativetype=5&phone=13802913949&product=vip_month&uuid=b508528a-5920-4b69-aac4-d89ae25e925c&t=1754893394520&source=generator"
        
        # 监控的域名和关键词
        self.target_domains = [
            'api.zzqz2024.com',
            'trace.zzqz2024.com', 
            'h5.syhy123.com',
            'alipay.com',
            'alipays.com'
        ]
        
        self.payment_keywords = [
            'signOrder', 'createOrder', 'payment', 'pay', 'order',
            'cashier', 'checkout', 'alipay', '支付'
        ]

    async def setup_browser(self):
        """设置浏览器"""
        self.playwright = await async_playwright().start()
        
        # 启动浏览器
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口
            args=[
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--enable-logging',
                '--log-level=0'
            ]
        )
        
        # 创建上下文，模拟移动设备
        self.context = await self.browser.new_context(
            user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1',
            viewport={'width': 375, 'height': 812},
            device_scale_factor=3,
            is_mobile=True,
            has_touch=True
        )
        
        # 创建页面
        self.page = await self.context.new_page()
        
        # 设置网络监听
        await self.setup_network_monitoring()
        
        print("✅ Playwright浏览器启动成功")

    async def setup_network_monitoring(self):
        """设置网络监控"""

        # 监听请求
        async def handle_request(request):
            timestamp = datetime.now().isoformat()
            url = request.url
            method = request.method
            headers = await request.all_headers()
            post_data = request.post_data

            # 记录请求
            request_data = {
                'timestamp': timestamp,
                'type': 'request',
                'method': method,
                'url': url,
                'headers': headers,
                'post_data': post_data,
                'resource_type': request.resource_type
            }

            self.requests_log.append(request_data)

            # 记录所有请求（不仅仅是目标域名）
            print(f"🌐 [{method}] {url}")

            # 检查是否是目标域名
            if any(domain in url for domain in self.target_domains):
                print(f"🎯 目标域名: [{method}] {url}")

                # 检查是否是支付相关请求
                if any(keyword in url.lower() for keyword in self.payment_keywords):
                    self.payment_requests.append(request_data)
                    print(f"💰 支付请求: [{method}] {url}")

                    if post_data:
                        print(f"📤 请求数据: {post_data}")

                # 检查是否是支付宝相关
                if 'alipay' in url.lower():
                    self.alipay_requests.append(request_data)
                    print(f"🚀 支付宝请求: {url}")
                    await self.analyze_alipay_url(url)

            # 检查爱奇艺相关链接
            if 'iqiyi' in url.lower() or 'qiyi' in url.lower():
                print(f"📺 爱奇艺相关: [{method}] {url}")
                await self.analyze_iqiyi_url(url)

            # 检查重定向和跳转链接
            if any(keyword in url.lower() for keyword in ['redirect', 'jump', 'goto', 'link']):
                print(f"🔗 跳转链接: [{method}] {url}")

            # 检查其他重要链接模式
            important_patterns = ['vip', 'member', 'subscribe', 'order', 'checkout']
            if any(pattern in url.lower() for pattern in important_patterns):
                print(f"⭐ 重要链接: [{method}] {url}")
        
        # 监听响应
        async def handle_response(response):
            timestamp = datetime.now().isoformat()
            url = response.url
            status = response.status
            headers = response.headers
            
            # 只处理目标域名的响应
            if any(domain in url for domain in self.target_domains):
                try:
                    # 获取响应体
                    body = await response.text()
                    
                    response_data = {
                        'timestamp': timestamp,
                        'type': 'response',
                        'url': url,
                        'status': status,
                        'headers': headers,
                        'body': body
                    }
                    
                    self.responses_log.append(response_data)
                    
                    # 如果是支付相关响应，详细记录
                    if any(keyword in url.lower() for keyword in self.payment_keywords):
                        print(f"📥 支付响应 [{status}]: {url}")
                        
                        # 尝试解析JSON响应
                        try:
                            if body and body.strip().startswith('{'):
                                json_data = json.loads(body)
                                print(f"📋 响应数据: {json.dumps(json_data, ensure_ascii=False, indent=2)}")
                                
                                # 检查是否包含支付宝链接
                                await self.extract_alipay_links(json_data)
                        except json.JSONDecodeError:
                            print(f"📋 响应数据 (非JSON): {body[:500]}...")
                            
                except Exception as e:
                    print(f"获取响应体失败: {e}")
        
        # 监听页面跳转
        async def handle_navigation(frame):
            url = frame.url
            if 'alipay' in url.lower():
                print(f"🚀 检测到支付宝页面跳转: {url}")
                await self.analyze_alipay_url(url)
        
        # 监听弹窗
        async def handle_dialog(dialog):
            print(f"🔔 检测到弹窗: {dialog.type}")
            print(f"   消息: {dialog.message}")
            print(f"   默认值: {dialog.default_value}")

            # 自动接受弹窗
            await dialog.accept()
            print("✅ 已自动接受弹窗")

        # 监听控制台日志
        async def handle_console(msg):
            if msg.type in ['error', 'warning']:
                print(f"🖥️ 控制台{msg.type}: {msg.text}")

        # 绑定事件监听器
        self.page.on('request', handle_request)
        self.page.on('response', handle_response)
        self.page.on('framenavigated', handle_navigation)
        self.page.on('dialog', handle_dialog)
        self.page.on('console', handle_console)

    async def extract_alipay_links(self, data):
        """从响应数据中提取支付宝链接"""
        def search_dict(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    if isinstance(value, str) and 'alipay' in value.lower():
                        print(f"🔗 发现支付宝链接 ({current_path}): {value}")
                        asyncio.create_task(self.analyze_alipay_url(value))
                    elif isinstance(value, (dict, list)):
                        search_dict(value, current_path)
                        
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    search_dict(item, current_path)
        
        search_dict(data)

    async def analyze_alipay_url(self, url):
        """分析支付宝URL"""
        print(f"🔍 分析支付宝链接: {url}")

        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)

            analysis = {
                'scheme': parsed.scheme,
                'netloc': parsed.netloc,
                'path': parsed.path,
                'params': params,
                'timestamp': datetime.now().isoformat()
            }

            print(f"📊 支付宝链接分析结果:")
            print(f"   协议: {parsed.scheme}")
            print(f"   域名: {parsed.netloc}")
            print(f"   路径: {parsed.path}")

            if params:
                print(f"   参数:")
                for key, values in params.items():
                    print(f"     {key}: {values[0] if values else 'N/A'}")

            # 特别关注爱奇艺相关参数
            if 'iqiyi' in url.lower() or 'qiyi' in url.lower():
                print(f"📺 这是爱奇艺相关的支付链接！")

            # 保存重要链接到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            with open(f"important_link_{timestamp}.txt", "w", encoding="utf-8") as f:
                f.write(f"时间: {datetime.now().isoformat()}\n")
                f.write(f"类型: 支付宝链接\n")
                f.write(f"完整URL: {url}\n")
                f.write(f"协议: {parsed.scheme}\n")
                f.write(f"域名: {parsed.netloc}\n")
                f.write(f"路径: {parsed.path}\n")
                if params:
                    f.write("参数:\n")
                    for key, values in params.items():
                        f.write(f"  {key}: {values[0] if values else 'N/A'}\n")

            print(f"💾 重要链接已保存到: important_link_{timestamp}.txt")

        except Exception as e:
            print(f"URL分析失败: {e}")

    async def analyze_iqiyi_url(self, url):
        """分析爱奇艺URL"""
        print(f"🔍 分析爱奇艺链接: {url}")

        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)

            print(f"� 爱奇艺链接分析结果:")
            print(f"   协议: {parsed.scheme}")
            print(f"   域名: {parsed.netloc}")
            print(f"   路径: {parsed.path}")

            if params:
                print(f"   参数:")
                for key, values in params.items():
                    print(f"     {key}: {values[0] if values else 'N/A'}")

            # 保存爱奇艺链接
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            with open(f"iqiyi_link_{timestamp}.txt", "w", encoding="utf-8") as f:
                f.write(f"时间: {datetime.now().isoformat()}\n")
                f.write(f"类型: 爱奇艺链接\n")
                f.write(f"完整URL: {url}\n")
                f.write(f"协议: {parsed.scheme}\n")
                f.write(f"域名: {parsed.netloc}\n")
                f.write(f"路径: {parsed.path}\n")
                if params:
                    f.write("参数:\n")
                    for key, values in params.items():
                        f.write(f"  {key}: {values[0] if values else 'N/A'}\n")

            print(f"💾 爱奇艺链接已保存到: iqiyi_link_{timestamp}.txt")

        except Exception as e:
            print(f"爱奇艺URL分析失败: {e}")

    async def simulate_user_actions(self):
        """模拟用户操作"""
        try:
            print("📱 开始模拟用户操作...")

            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)

            # 截图保存当前页面状态
            await self.page.screenshot(path=f"page_initial_{int(time.time())}.png")
            print("📸 已保存初始页面截图")

            # 查找"立即领取"按钮
            receive_selectors = [
                'button:has-text("立即领取")',
                'button:has-text("领取")',
                '.receive-btn',
                '.get-btn',
                'button:has-text("免费领取")',
                'button:has-text("点击领取")',
                '[onclick*="领取"]',
                '[onclick*="receive"]'
            ]

            receive_button = None
            for selector in receive_selectors:
                try:
                    receive_button = await self.page.query_selector(selector)
                    if receive_button:
                        print(f"🎯 找到领取按钮: {selector}")
                        break
                except:
                    continue

            if receive_button:
                print("🔘 点击立即领取按钮...")
                await receive_button.click()
                await asyncio.sleep(2)

                # 截图保存点击后的状态
                await self.page.screenshot(path=f"page_after_receive_{int(time.time())}.png")
                print("📸 已保存点击领取后的截图")

                # 等待可能的弹窗出现
                await asyncio.sleep(1)

                # 查找弹窗中的确认按钮
                popup_selectors = [
                    'button:has-text("确认")',
                    'button:has-text("确定")',
                    'button:has-text("领取")',
                    'button:has-text("继续")',
                    '.popup-confirm',
                    '.modal-confirm',
                    '.dialog-confirm'
                ]

                popup_button = None
                for selector in popup_selectors:
                    try:
                        popup_button = await self.page.query_selector(selector)
                        if popup_button:
                            print(f"🎯 找到弹窗确认按钮: {selector}")
                            break
                    except:
                        continue

                if popup_button:
                    print("🔘 点击弹窗确认按钮...")
                    await popup_button.click()
                    await asyncio.sleep(2)

                    # 截图保存弹窗确认后的状态
                    await self.page.screenshot(path=f"page_after_popup_{int(time.time())}.png")
                    print("📸 已保存弹窗确认后的截图")

            # 查找手机号输入框
            phone_selectors = [
                'input[type="tel"]',
                'input[name*="phone"]',
                'input[placeholder*="手机"]',
                'input[id*="phone"]',
                '.phone-input',
                '#phone'
            ]

            phone_input = None
            for selector in phone_selectors:
                try:
                    phone_input = await self.page.query_selector(selector)
                    if phone_input:
                        print(f"🎯 找到手机号输入框: {selector}")
                        break
                except:
                    continue

            if phone_input:
                await phone_input.clear()
                await phone_input.fill("13802913949")
                print("📞 已输入手机号: 13802913949")
                await asyncio.sleep(1)

            # 查找提交/下单按钮
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                '.submit-btn',
                '.order-btn',
                'button:has-text("提交")',
                'button:has-text("下单")',
                'button:has-text("确认")',
                'button:has-text("立即下单")'
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = await self.page.query_selector(selector)
                    if submit_button:
                        print(f"🎯 找到提交按钮: {selector}")
                        break
                except:
                    continue

            if submit_button:
                await submit_button.click()
                print("🔘 已点击提交按钮")
                await asyncio.sleep(3)

                # 截图保存提交后的状态
                await self.page.screenshot(path=f"page_after_submit_{int(time.time())}.png")
                print("📸 已保存提交后的截图")

            # 查找支付按钮
            pay_selectors = [
                '.pay-btn',
                '.payment-btn',
                '.alipay-btn',
                'button:has-text("支付")',
                'button:has-text("确认支付")',
                'button:has-text("立即支付")',
                'button:has-text("去支付")'
            ]

            pay_button = None
            for selector in pay_selectors:
                try:
                    pay_button = await self.page.query_selector(selector)
                    if pay_button:
                        print(f"🎯 找到支付按钮: {selector}")
                        break
                except:
                    continue

            if pay_button:
                await pay_button.click()
                print("💰 已点击支付按钮")
                await asyncio.sleep(3)

                # 截图保存支付后的状态
                await self.page.screenshot(path=f"page_after_pay_{int(time.time())}.png")
                print("📸 已保存支付后的截图")

            # 检查是否有手机端限制提示
            await self.check_mobile_restriction()

        except Exception as e:
            print(f"用户操作模拟失败: {e}")

    async def check_mobile_restriction(self):
        """检查手机端限制"""
        try:
            # 查找手机端限制相关的文本
            restriction_texts = [
                "请在手机端",
                "仅限手机",
                "移动端访问",
                "手机打开",
                "请使用手机",
                "mobile only",
                "手机扫码"
            ]

            page_content = await self.page.content()

            for text in restriction_texts:
                if text in page_content:
                    print(f"🚫 检测到手机端限制: {text}")

                    # 查找相关的链接或二维码
                    qr_selectors = [
                        'img[src*="qr"]',
                        'img[alt*="二维码"]',
                        'img[alt*="扫码"]',
                        '.qr-code',
                        '.qrcode'
                    ]

                    for selector in qr_selectors:
                        try:
                            qr_element = await self.page.query_selector(selector)
                            if qr_element:
                                qr_src = await qr_element.get_attribute('src')
                                print(f"📱 发现二维码: {qr_src}")
                        except:
                            continue

                    break

        except Exception as e:
            print(f"检查手机端限制失败: {e}")

    async def continuous_monitoring(self, duration=60):
        """持续监控"""
        print(f"⏰ 开始持续监控，等待{duration}秒...")
        
        start_time = time.time()
        
        while (time.time() - start_time) < duration:
            try:
                # 检查页面URL变化
                current_url = self.page.url
                if 'alipay' in current_url.lower():
                    print(f"🚀 检测到支付宝页面跳转: {current_url}")
                    await self.analyze_alipay_url(current_url)
                
                # 检查页面标题变化
                title = await self.page.title()
                if '支付宝' in title or 'alipay' in title.lower():
                    print(f"🚀 检测到支付宝页面: {title}")
                
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"持续监控错误: {e}")
                break

    async def start_monitoring(self):
        """开始监控"""
        try:
            # 设置浏览器
            await self.setup_browser()
            
            print("🚀 开始监控支付流程...")
            
            # 访问目标页面
            print(f"📱 访问页面: {self.target_url}")
            await self.page.goto(self.target_url)
            
            # 等待页面加载
            await asyncio.sleep(3)
            
            # 模拟用户操作
            await self.simulate_user_actions()
            
            # 持续监控
            await self.continuous_monitoring()
            
        except Exception as e:
            print(f"❌ 监控过程出错: {e}")
        finally:
            await self.stop_monitoring()

    async def stop_monitoring(self):
        """停止监控"""
        print("⏹️ 停止监控...")
        
        if self.browser:
            await self.browser.close()
        
        if self.playwright:
            await self.playwright.stop()
        
        # 保存监控结果
        await self.save_results()

    async def save_results(self):
        """保存监控结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        results = {
            'timestamp': timestamp,
            'target_url': self.target_url,
            'total_requests': len(self.requests_log),
            'total_responses': len(self.responses_log),
            'payment_requests': len(self.payment_requests),
            'alipay_requests': len(self.alipay_requests),
            'all_requests': self.requests_log,
            'all_responses': self.responses_log,
            'payment_requests_detail': self.payment_requests,
            'alipay_requests_detail': self.alipay_requests
        }
        
        filename = f"playwright_monitor_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"💾 监控结果已保存到: {filename}")
            
            # 打印统计信息
            print("📊 监控统计:")
            print(f"   总请求数: {len(self.requests_log)}")
            print(f"   总响应数: {len(self.responses_log)}")
            print(f"   支付相关请求: {len(self.payment_requests)}")
            print(f"   支付宝相关请求: {len(self.alipay_requests)}")
            
        except Exception as e:
            print(f"保存结果失败: {e}")

async def main():
    """主函数"""
    print("🔍 Playwright支付流程监控器")
    print("=" * 50)
    
    monitor = PlaywrightPaymentMonitor()
    
    try:
        await monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断监控")
        await monitor.stop_monitoring()
    except Exception as e:
        print(f"❌ 监控失败: {e}")
        await monitor.stop_monitoring()

if __name__ == "__main__":
    asyncio.run(main())
