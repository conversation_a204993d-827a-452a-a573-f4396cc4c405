/**
 * H5下单系统 - 后端UA验证API
 * 基于Express.js的示例实现
 */

const express = require('express');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

const app = express();

// 中间件配置
app.use(helmet()); // 安全头
app.use(cors()); // 跨域
app.use(express.json({ limit: '10mb' }));

// 限流配置
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: { error: '请求过于频繁，请稍后再试' }
});
app.use('/api/', limiter);

// UA检测配置
const UA_CONFIG = {
    // 桌面设备特征
    desktopPatterns: [
        /Windows NT/i,
        /Macintosh/i,
        /Intel Mac OS X/i,
        /Linux x86_64/i,
        /X11/i,
        /WOW64/i,
        /Win64/i,
        /x64/i,
        /AMD64/i,
        /desktop/i
    ],
    
    // 移动设备特征
    mobilePatterns: [
        /Mobile/i,
        /Android/i,
        /iPhone/i,
        /iPad/i,
        /iPod/i,
        /BlackBerry/i,
        /Windows Phone/i,
        /Opera Mini/i,
        /IEMobile/i,
        /Mobile Safari/i,
        /webOS/i,
        /Symbian/i
    ],
    
    // 可疑特征
    suspiciousPatterns: [
        /HeadlessChrome/i,
        /PhantomJS/i,
        /SlimerJS/i,
        /Selenium/i,
        /WebDriver/i,
        /Bot/i,
        /Spider/i,
        /Crawler/i,
        /curl/i,
        /wget/i,
        /python/i,
        /java/i,
        /node/i
    ],
    
    // 黑名单UA（从Excel数据中提取的已知桌面UA）
    blacklist: [
        // 这里可以添加从Excel分析中得到的桌面UA黑名单
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        // ... 更多黑名单项
    ],
    
    // 白名单（允许的特殊情况）
    whitelist: [
        // 可以添加一些特殊的移动设备UA
    ]
};

// 增强版UA分析类
class EnhancedUAAnalyzer {
    static analyze(userAgent, deviceFingerprint = {}, behaviorData = {}, req = {}) {
        const ua = userAgent || '';
        const analysis = {
            userAgent: ua,
            fingerprint: deviceFingerprint,
            behavior: behaviorData,
            httpHeaders: this.analyzeHTTPHeaders(req),
            ipAnalysis: this.analyzeIP(req),
            riskScore: 0,
            riskFactors: [],
            decision: 'allow',
            confidence: 0,
            timestamp: new Date().toISOString()
        };

        // 多层检测
        this.analyzeUserAgent(analysis);
        this.analyzeDeviceFingerprint(analysis);
        this.analyzeBehaviorPattern(analysis);
        this.analyzeHTTPConsistency(analysis);
        this.analyzeIPRisk(analysis);
        this.detectAutomation(analysis);

        // 计算最终风险分数和决策
        this.calculateFinalDecision(analysis);

        return analysis;
    }

    // 分析User-Agent
    static analyzeUserAgent(analysis) {
        const ua = analysis.userAgent.toLowerCase();

        // 检查黑名单
        if (this.isInBlacklist(ua)) {
            analysis.riskScore += 100;
            analysis.riskFactors.push('UA在黑名单中');
            return;
        }

        // 检查白名单
        if (this.isInWhitelist(ua)) {
            analysis.riskScore -= 30;
            analysis.riskFactors.push('UA在白名单中');
            return;
        }

        // 检测自动化工具特征
        const automationPatterns = [
            /headlesschrome/i, /phantomjs/i, /selenium/i, /webdriver/i,
            /bot/i, /spider/i, /crawler/i, /scraper/i,
            /python/i, /java/i, /curl/i, /wget/i, /node/i
        ];

        const automationMatches = automationPatterns.filter(pattern => pattern.test(ua));
        if (automationMatches.length > 0) {
            analysis.riskScore += 80;
            analysis.riskFactors.push(`检测到自动化工具特征: ${automationMatches.length}个`);
        }

        // 检测桌面操作系统
        const desktopOS = [
            /windows nt/i, /macintosh/i, /intel mac os x/i,
            /linux x86_64/i, /x11/i, /ubuntu/i, /fedora/i
        ];

        const desktopMatches = desktopOS.filter(pattern => pattern.test(ua));
        if (desktopMatches.length > 0 && !/mobile/i.test(ua)) {
            analysis.riskScore += 40;
            analysis.riskFactors.push(`检测到桌面操作系统: ${desktopMatches.length}个`);
        }

        // 检测移动设备特征
        const mobilePatterns = [
            /mobile/i, /android/i, /iphone/i, /ipad/i, /ipod/i,
            /blackberry/i, /windows phone/i, /opera mini/i
        ];

        const mobileMatches = mobilePatterns.filter(pattern => pattern.test(ua));
        if (mobileMatches.length > 0) {
            analysis.riskScore -= 25;
            analysis.riskFactors.push(`检测到移动设备特征: ${mobileMatches.length}个`);
        }

        // UA格式异常检测
        if (ua.length < 50 || ua.length > 500) {
            analysis.riskScore += 15;
            analysis.riskFactors.push('UA长度异常');
        }

        // 检测常见的UA伪造模式
        if (this.detectFakeUA(ua)) {
            analysis.riskScore += 30;
            analysis.riskFactors.push('检测到伪造UA模式');
        }
    }

    // 分析设备指纹
    static analyzeDeviceFingerprint(analysis) {
        const fp = analysis.fingerprint;
        if (!fp || Object.keys(fp).length === 0) {
            analysis.riskScore += 20;
            analysis.riskFactors.push('缺少设备指纹数据');
            return;
        }

        // 屏幕信息分析
        if (fp.screen) {
            const { width, height } = fp.screen;
            if (width > 1024 || height > 1024) {
                analysis.riskScore += 25;
                analysis.riskFactors.push(`大屏幕尺寸: ${width}x${height}`);
            }

            // 检查屏幕比例
            const aspectRatio = Math.max(width, height) / Math.min(width, height);
            if (aspectRatio > 2.5) {
                analysis.riskScore += 10;
                analysis.riskFactors.push('异常屏幕比例');
            }
        }

        // 触摸能力分析
        if (fp.touch) {
            if (!fp.touch.touchSupport || fp.touch.maxTouchPoints === 0) {
                analysis.riskScore += 30;
                analysis.riskFactors.push('不支持触摸操作');
            }
        }

        // 传感器分析
        if (fp.sensors) {
            const mobileSensors = ['deviceMotion', 'deviceOrientation'];
            const hasMobileSensors = mobileSensors.some(sensor => fp.sensors[sensor]);

            if (!hasMobileSensors) {
                analysis.riskScore += 20;
                analysis.riskFactors.push('缺少移动设备传感器');
            }

            // 检查传感器数据真实性
            if (fp.sensors.gyroscopeReal && fp.sensors.gyroscopeReal.variance === 0) {
                analysis.riskScore += 25;
                analysis.riskFactors.push('陀螺仪数据异常');
            }
        }

        // 硬件信息分析
        if (fp.hardware) {
            // 检查内存大小（移动设备通常内存较小）
            if (fp.hardware.memory && fp.hardware.memory > 8) {
                analysis.riskScore += 15;
                analysis.riskFactors.push('内存容量过大');
            }

            // 检查CPU核心数
            if (fp.hardware.cores && fp.hardware.cores > 8) {
                analysis.riskScore += 10;
                analysis.riskFactors.push('CPU核心数过多');
            }

            // 检查电池API
            if (!fp.hardware.battery) {
                analysis.riskScore += 10;
                analysis.riskFactors.push('缺少电池API');
            }
        }

        // 浏览器特性分析
        if (fp.browser) {
            // WebDriver检测
            if (fp.browser.webdriver) {
                analysis.riskScore += 50;
                analysis.riskFactors.push('检测到WebDriver');
            }

            // 插件检测（移动设备通常插件较少）
            if (fp.browser.plugins && fp.browser.plugins.length > 5) {
                analysis.riskScore += 15;
                analysis.riskFactors.push('插件数量过多');
            }
        }
    }

    // 分析行为模式
    static analyzeBehaviorPattern(analysis) {
        const behavior = analysis.behavior;
        if (!behavior || Object.keys(behavior).length === 0) {
            analysis.riskScore += 15;
            analysis.riskFactors.push('缺少行为数据');
            return;
        }

        // 输入方法一致性
        if (behavior.inputMethodConsistency === 'mouse_only') {
            analysis.riskScore += 25;
            analysis.riskFactors.push('仅使用鼠标交互');
        } else if (behavior.inputMethodConsistency === 'mixed') {
            analysis.riskScore += 10;
            analysis.riskFactors.push('混合输入方式可疑');
        }

        // 触摸压力检测
        if (behavior.touchPressureVariance === 0 && behavior.hasTouchActivity) {
            analysis.riskScore += 20;
            analysis.riskFactors.push('触摸压力数据异常');
        }

        // 滚动模式分析
        if (behavior.scrollPattern) {
            // 移动设备滚动通常有惯性，速度变化较大
            if (behavior.scrollPattern.velocityVariance < 0.1) {
                analysis.riskScore += 15;
                analysis.riskFactors.push('滚动模式异常');
            }
        }

        // 方向变化检测
        if (!behavior.hasOrientationChange) {
            analysis.riskScore += 10;
            analysis.riskFactors.push('缺少设备方向变化');
        }
    }

    // 分析HTTP头一致性
    static analyzeHTTPConsistency(analysis) {
        const headers = analysis.httpHeaders;

        // Accept头检查
        if (headers.accept && !headers.accept.includes('text/html')) {
            analysis.riskScore += 15;
            analysis.riskFactors.push('Accept头异常');
        }

        // Accept-Language检查
        if (!headers.acceptLanguage) {
            analysis.riskScore += 10;
            analysis.riskFactors.push('缺少Accept-Language头');
        }

        // Accept-Encoding检查
        if (headers.acceptEncoding && !headers.acceptEncoding.includes('gzip')) {
            analysis.riskScore += 10;
            analysis.riskFactors.push('Accept-Encoding异常');
        }

        // Connection头检查
        if (headers.connection && headers.connection.toLowerCase() !== 'keep-alive') {
            analysis.riskScore += 5;
            analysis.riskFactors.push('Connection头异常');
        }
    }

    // 分析IP风险
    static analyzeIPRisk(analysis) {
        const ip = analysis.ipAnalysis;

        if (ip.isDataCenter) {
            analysis.riskScore += 30;
            analysis.riskFactors.push('数据中心IP');
        }

        if (ip.isProxy) {
            analysis.riskScore += 25;
            analysis.riskFactors.push('代理IP');
        }

        if (ip.ipReputation < 50) {
            analysis.riskScore += 20;
            analysis.riskFactors.push('IP信誉度低');
        }
    }

    // 检测自动化工具
    static detectAutomation(analysis) {
        const fp = analysis.fingerprint;

        // WebDriver检测
        if (fp.browser && fp.browser.webdriver) {
            analysis.riskScore += 50;
            analysis.riskFactors.push('WebDriver检测');
        }

        // 无头浏览器检测
        if (fp.browser && fp.browser.plugins && fp.browser.plugins.length === 0) {
            analysis.riskScore += 20;
            analysis.riskFactors.push('可能是无头浏览器');
        }

        // Canvas指纹检测
        if (fp.browser && fp.browser.canvas === null) {
            analysis.riskScore += 15;
            analysis.riskFactors.push('Canvas指纹异常');
        }

        // WebGL检测
        if (fp.browser && fp.browser.webgl === null) {
            analysis.riskScore += 15;
            analysis.riskFactors.push('WebGL不可用');
        }
    }

    // 检测伪造UA
    static detectFakeUA(ua) {
        // 检测常见的伪造模式
        const fakePatterns = [
            // 版本号异常
            /Chrome\/999/i,
            /Firefox\/999/i,
            /Safari\/999/i,

            // 格式异常
            /Mozilla\/5\.0.*Chrome.*Safari.*Chrome/i,
            /Mozilla\/4\.0.*Chrome/i,

            // 移动设备但包含桌面特征
            /Mobile.*Windows NT/i,
            /iPhone.*X11/i,
            /Android.*Macintosh/i
        ];

        return fakePatterns.some(pattern => pattern.test(ua));
    }

    // 分析HTTP请求头
    static analyzeHTTPHeaders(req) {
        return {
            accept: req.get('Accept'),
            acceptLanguage: req.get('Accept-Language'),
            acceptEncoding: req.get('Accept-Encoding'),
            connection: req.get('Connection'),
            cacheControl: req.get('Cache-Control'),
            pragma: req.get('Pragma'),
            upgradeInsecureRequests: req.get('Upgrade-Insecure-Requests'),
            dnt: req.get('DNT'),
            secFetchSite: req.get('Sec-Fetch-Site'),
            secFetchMode: req.get('Sec-Fetch-Mode'),
            secFetchDest: req.get('Sec-Fetch-Dest')
        };
    }

    // 分析IP地址
    static analyzeIP(req) {
        const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;

        return {
            ip: clientIP,
            isDataCenter: this.checkDataCenterIP(clientIP),
            isProxy: this.checkProxyIP(clientIP),
            ipReputation: this.getIPReputation(clientIP),
            geoLocation: this.getGeoLocation(clientIP)
        };
    }

    // 检查数据中心IP（简化实现）
    static checkDataCenterIP(ip) {
        // 这里应该集成真实的数据中心IP数据库
        const dataCenterRanges = [
            '*******/24',      // Google DNS
            '*******/24',      // Cloudflare
            '************/24'  // OpenDNS
        ];

        // 简化检查逻辑
        return dataCenterRanges.some(range => ip.startsWith(range.split('/')[0].slice(0, -1)));
    }

    // 检查代理IP（简化实现）
    static checkProxyIP(ip) {
        // 这里应该集成代理IP检测服务
        return false; // 简化实现
    }

    // 获取IP信誉度（简化实现）
    static getIPReputation(ip) {
        // 这里应该集成IP信誉度服务
        return 75; // 简化实现，返回中等信誉度
    }

    // 获取地理位置（简化实现）
    static getGeoLocation(ip) {
        // 这里应该集成地理位置服务
        return { country: 'Unknown', city: 'Unknown' };
    }

    // 计算最终决策
    static calculateFinalDecision(analysis) {
        const score = analysis.riskScore;

        // 计算置信度
        analysis.confidence = Math.min(Math.abs(score - 50) / 50 * 100, 100);

        if (score >= 80) {
            analysis.decision = 'block';
            analysis.riskLevel = 'critical';
        } else if (score >= 60) {
            analysis.decision = 'block';
            analysis.riskLevel = 'high';
        } else if (score >= 40) {
            analysis.decision = 'review';
            analysis.riskLevel = 'medium';
        } else if (score >= 20) {
            analysis.decision = 'allow';
            analysis.riskLevel = 'low';
        } else {
            analysis.decision = 'allow';
            analysis.riskLevel = 'very_low';
        }
    }

    static isInBlacklist(ua) {
        return UA_CONFIG.blacklist.some(blackUA => ua.includes(blackUA));
    }

    static isInWhitelist(ua) {
        return UA_CONFIG.whitelist.some(whiteUA => ua.includes(whiteUA));
    }
}

// 日志记录
class SecurityLogger {
    static log(type, data, req) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            type: type,
            ip: req.ip || req.connection.remoteAddress,
            userAgent: req.get('User-Agent'),
            data: data
        };

        console.log(`[${type.toUpperCase()}]`, JSON.stringify(logEntry));
        
        // 这里可以集成到日志系统，如ELK、Splunk等
        // await logToDatabase(logEntry);
        // await sendToLogService(logEntry);
    }
}

// API路由

// 设备验证接口
app.post('/api/device/verify', (req, res) => {
    try {
        const { deviceFingerprint, behaviorData } = req.body;
        const userAgent = req.get('User-Agent');

        // 使用增强版分析器
        const analysis = EnhancedUAAnalyzer.analyze(userAgent, deviceFingerprint, behaviorData, req);

        // 记录日志
        SecurityLogger.log('device_verify', {
            analysis,
            deviceFingerprint,
            behaviorData,
            decision: analysis.decision
        }, req);

        // 返回结果
        res.json({
            success: true,
            analysis: {
                decision: analysis.decision,
                riskLevel: analysis.riskLevel,
                riskScore: analysis.riskScore,
                riskFactors: analysis.riskFactors,
                confidence: analysis.confidence
            },
            timestamp: analysis.timestamp
        });

    } catch (error) {
        console.error('设备验证失败:', error);
        res.status(500).json({
            success: false,
            error: '设备验证失败'
        });
    }
});

// 订单创建接口
app.post('/api/order/create', (req, res) => {
    try {
        const { productType, deviceFingerprint, behaviorData, securityCheck } = req.body;
        const userAgent = req.get('User-Agent');

        // 使用增强版分析器重新验证设备
        const analysis = EnhancedUAAnalyzer.analyze(userAgent, deviceFingerprint, behaviorData, req);

        if (analysis.decision === 'block') {
            SecurityLogger.log('order_blocked', {
                productType,
                analysis,
                reason: 'Enhanced device verification failed'
            }, req);

            return res.status(403).json({
                success: false,
                error: '设备安全验证失败，无法创建订单',
                code: 'DEVICE_BLOCKED',
                details: {
                    riskScore: analysis.riskScore,
                    riskFactors: analysis.riskFactors.slice(0, 3), // 只返回前3个风险因素
                    confidence: analysis.confidence
                }
            });
        }

        if (analysis.decision === 'review') {
            SecurityLogger.log('order_review', {
                productType,
                analysis,
                reason: 'Device needs enhanced manual review'
            }, req);

            return res.status(202).json({
                success: false,
                error: '订单需要人工安全审核',
                code: 'MANUAL_REVIEW_REQUIRED',
                contactInfo: 'your_wechat_id',
                details: {
                    riskScore: analysis.riskScore,
                    estimatedReviewTime: '1-2小时'
                }
            });
        }

        // 创建订单
        const orderId = 'IQ' + Date.now().toString(36).toUpperCase();
        const order = {
            orderId,
            productType,
            amount: productType === 'iqiyi_vip' ? 19.8 : 39.8,
            productName: productType === 'iqiyi_vip' ? '爱奇艺VIP月卡' : '爱奇艺体育月卡',
            paymentUrl: `https://pay.iqiyi.com/order/${orderId}?product=${productType}`,
            status: 'pending',
            createdAt: new Date().toISOString(),
            securityLevel: analysis.riskLevel
        };

        SecurityLogger.log('order_created', {
            order,
            analysis,
            deviceFingerprint: Object.keys(deviceFingerprint || {}),
            behaviorData: Object.keys(behaviorData || {})
        }, req);

        res.json({
            success: true,
            order,
            security: {
                riskLevel: analysis.riskLevel,
                confidence: analysis.confidence
            }
        });

    } catch (error) {
        console.error('订单创建失败:', error);
        res.status(500).json({
            success: false,
            error: '订单创建失败'
        });
    }
});

// 获取统计信息接口
app.get('/api/stats', (req, res) => {
    // 这里可以返回实时的统计信息
    res.json({
        success: true,
        stats: {
            totalRequests: 1000,
            blockedRequests: 150,
            allowedRequests: 800,
            reviewRequests: 50,
            blockRate: '15%'
        }
    });
});

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// 错误处理
app.use((err, req, res, next) => {
    console.error('API错误:', err);
    res.status(500).json({
        success: false,
        error: '服务器内部错误'
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: '接口不存在'
    });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`🚀 H5下单系统API服务启动成功`);
    console.log(`📡 服务地址: http://localhost:${PORT}`);
    console.log(`🔒 安全验证已启用`);
});

module.exports = app;
