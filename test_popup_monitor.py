#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试弹窗和手机端检测的监控器
"""

import asyncio
import time
from datetime import datetime
from playwright.async_api import async_playwright

class PopupTestMonitor:
    def __init__(self):
        self.logs = []
        self.page = None
        self.browser = None
        self.context = None
        
        # 目标URL - 你提供的链接
        self.target_url = "https://h5.syhy123.com/aqy_msv2/?a=0ba8c5e5f1d60cf6658f6a266a6feb2b&projectid=7526720476899622948&promotionid=7526720211249496127&creativetype=5&phone=13802913949&product=vip_month&uuid=b508528a-5920-4b69-aac4-d89ae25e925c&t=1754893394520&source=generator"

    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs.append(log_entry)
        print(log_entry)

    async def setup_browser(self):
        """设置浏览器"""
        self.playwright = await async_playwright().start()
        
        # 启动浏览器
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口
            args=['--disable-web-security']
        )
        
        # 创建上下文，模拟移动设备
        self.context = await self.browser.new_context(
            user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1',
            viewport={'width': 375, 'height': 812},
            device_scale_factor=3,
            is_mobile=True,
            has_touch=True
        )
        
        # 创建页面
        self.page = await self.context.new_page()
        
        # 设置事件监听
        await self.setup_event_listeners()
        
        self.log("✅ 浏览器启动成功")

    async def setup_event_listeners(self):
        """设置事件监听"""
        
        # 监听所有网络请求
        async def handle_request(request):
            url = request.url
            method = request.method
            
            # 记录所有请求
            self.log(f"🌐 [{method}] {url}")
            
            # 特别关注重要域名
            important_domains = ['iqiyi', 'qiyi', 'alipay', 'api.zzqz2024.com', 'h5.syhy123.com']
            if any(domain in url.lower() for domain in important_domains):
                self.log(f"⭐ 重要请求: [{method}] {url}")
                
                # 保存重要链接
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                with open(f"captured_url_{timestamp}.txt", "w", encoding="utf-8") as f:
                    f.write(f"时间: {datetime.now().isoformat()}\n")
                    f.write(f"方法: {method}\n")
                    f.write(f"URL: {url}\n")
                    f.write(f"域名匹配: {[d for d in important_domains if d in url.lower()]}\n")
                
                self.log(f"💾 重要链接已保存: captured_url_{timestamp}.txt")
        
        # 监听响应
        async def handle_response(response):
            url = response.url
            status = response.status
            
            # 只记录重要响应
            important_domains = ['iqiyi', 'qiyi', 'alipay', 'api.zzqz2024.com', 'h5.syhy123.com']
            if any(domain in url.lower() for domain in important_domains):
                self.log(f"📥 响应 [{status}]: {url}")
                
                try:
                    # 尝试获取响应体
                    body = await response.text()
                    if body and len(body) > 0:
                        # 查找重要信息
                        if 'alipay' in body.lower() or 'iqiyi' in body.lower():
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                            with open(f"response_body_{timestamp}.txt", "w", encoding="utf-8") as f:
                                f.write(f"时间: {datetime.now().isoformat()}\n")
                                f.write(f"URL: {url}\n")
                                f.write(f"状态: {status}\n")
                                f.write(f"响应体:\n{body}\n")
                            
                            self.log(f"💾 重要响应已保存: response_body_{timestamp}.txt")
                except:
                    pass
        
        # 监听弹窗
        async def handle_dialog(dialog):
            self.log(f"🔔 检测到弹窗!")
            self.log(f"   类型: {dialog.type}")
            self.log(f"   消息: {dialog.message}")
            
            # 保存弹窗信息
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            with open(f"dialog_{timestamp}.txt", "w", encoding="utf-8") as f:
                f.write(f"时间: {datetime.now().isoformat()}\n")
                f.write(f"类型: {dialog.type}\n")
                f.write(f"消息: {dialog.message}\n")
                f.write(f"默认值: {dialog.default_value}\n")
            
            self.log(f"💾 弹窗信息已保存: dialog_{timestamp}.txt")
            
            # 自动接受弹窗
            await dialog.accept()
            self.log("✅ 已自动接受弹窗")
        
        # 监听控制台消息
        async def handle_console(msg):
            if msg.type in ['error', 'warning', 'log']:
                self.log(f"🖥️ 控制台[{msg.type}]: {msg.text}")
        
        # 监听页面跳转
        async def handle_navigation(frame):
            url = frame.url
            self.log(f"🔄 页面跳转: {url}")
            
            # 检查是否跳转到重要页面
            if 'alipay' in url.lower() or 'iqiyi' in url.lower():
                self.log(f"🚀 重要页面跳转: {url}")
                
                # 保存跳转信息
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                with open(f"navigation_{timestamp}.txt", "w", encoding="utf-8") as f:
                    f.write(f"时间: {datetime.now().isoformat()}\n")
                    f.write(f"跳转URL: {url}\n")
                
                self.log(f"💾 跳转信息已保存: navigation_{timestamp}.txt")
        
        # 绑定事件
        self.page.on('request', handle_request)
        self.page.on('response', handle_response)
        self.page.on('dialog', handle_dialog)
        self.page.on('console', handle_console)
        self.page.on('framenavigated', handle_navigation)

    async def test_popup_flow(self):
        """测试弹窗流程"""
        try:
            self.log("🚀 开始测试弹窗流程")
            
            # 访问目标页面
            self.log(f"📱 访问页面: {self.target_url}")
            await self.page.goto(self.target_url)
            
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            # 截图保存初始状态
            await self.page.screenshot(path=f"initial_{int(time.time())}.png")
            self.log("📸 已保存初始页面截图")
            
            # 查找并点击"立即领取"按钮
            receive_selectors = [
                'text=立即领取',
                'text=领取',
                'text=免费领取',
                'text=点击领取',
                '.receive-btn',
                '.get-btn',
                '[onclick*="领取"]'
            ]
            
            clicked = False
            for selector in receive_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        self.log(f"🎯 找到领取按钮: {selector}")
                        await element.click()
                        self.log("🔘 已点击立即领取按钮")
                        clicked = True
                        break
                except:
                    continue
            
            if not clicked:
                # 尝试通过文本查找
                try:
                    await self.page.click('text=立即领取')
                    self.log("🔘 通过文本找到并点击了立即领取按钮")
                    clicked = True
                except:
                    pass
            
            if clicked:
                # 等待弹窗或页面变化
                await asyncio.sleep(2)
                
                # 截图保存点击后状态
                await self.page.screenshot(path=f"after_click_{int(time.time())}.png")
                self.log("📸 已保存点击后的截图")
                
                # 查找弹窗中的确认按钮
                popup_selectors = [
                    'text=确认',
                    'text=确定',
                    'text=领取',
                    'text=继续',
                    '.popup-confirm',
                    '.modal-confirm'
                ]
                
                for selector in popup_selectors:
                    try:
                        element = await self.page.query_selector(selector)
                        if element:
                            self.log(f"🎯 找到弹窗确认按钮: {selector}")
                            await element.click()
                            self.log("🔘 已点击弹窗确认按钮")
                            await asyncio.sleep(2)
                            break
                    except:
                        continue
            
            # 检查页面内容，查找手机端限制信息
            await self.check_mobile_restriction()
            
            # 继续监控一段时间
            self.log("⏰ 继续监控30秒...")
            await asyncio.sleep(30)
            
        except Exception as e:
            self.log(f"❌ 测试过程出错: {e}")

    async def check_mobile_restriction(self):
        """检查手机端限制"""
        try:
            page_content = await self.page.content()
            
            # 查找手机端限制相关文本
            restriction_keywords = [
                "请在手机端",
                "仅限手机",
                "移动端访问",
                "手机打开",
                "请使用手机",
                "mobile only",
                "手机扫码",
                "扫码打开",
                "二维码"
            ]
            
            found_restrictions = []
            for keyword in restriction_keywords:
                if keyword in page_content:
                    found_restrictions.append(keyword)
            
            if found_restrictions:
                self.log(f"🚫 检测到手机端限制: {found_restrictions}")
                
                # 保存页面内容
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                with open(f"mobile_restriction_{timestamp}.html", "w", encoding="utf-8") as f:
                    f.write(page_content)
                
                self.log(f"💾 页面内容已保存: mobile_restriction_{timestamp}.html")
                
                # 查找二维码
                qr_selectors = [
                    'img[src*="qr"]',
                    'img[alt*="二维码"]',
                    'img[alt*="扫码"]',
                    '.qr-code',
                    '.qrcode'
                ]
                
                for selector in qr_selectors:
                    try:
                        qr_element = await self.page.query_selector(selector)
                        if qr_element:
                            qr_src = await qr_element.get_attribute('src')
                            self.log(f"📱 发现二维码: {qr_src}")
                    except:
                        continue
            else:
                self.log("✅ 未检测到明显的手机端限制")
                
        except Exception as e:
            self.log(f"检查手机端限制失败: {e}")

    async def save_logs(self):
        """保存日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"popup_test_log_{timestamp}.txt"
        
        with open(filename, "w", encoding="utf-8") as f:
            f.write("弹窗测试监控日志\n")
            f.write("=" * 50 + "\n")
            f.write(f"测试时间: {datetime.now().isoformat()}\n")
            f.write(f"目标URL: {self.target_url}\n")
            f.write("=" * 50 + "\n\n")
            
            for log_entry in self.logs:
                f.write(log_entry + "\n")
        
        self.log(f"💾 完整日志已保存: {filename}")

    async def run_test(self):
        """运行测试"""
        try:
            await self.setup_browser()
            await self.test_popup_flow()
        except Exception as e:
            self.log(f"❌ 测试失败: {e}")
        finally:
            await self.save_logs()
            
            if self.browser:
                await self.browser.close()
            
            if self.playwright:
                await self.playwright.stop()
            
            self.log("⏹️ 测试完成")

async def main():
    """主函数"""
    print("🔍 弹窗和手机端检测测试器")
    print("=" * 50)
    
    monitor = PopupTestMonitor()
    await monitor.run_test()

if __name__ == "__main__":
    asyncio.run(main())
