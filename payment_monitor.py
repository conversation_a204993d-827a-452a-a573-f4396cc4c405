#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支付流程接口监控器
使用Selenium + mitmproxy监控完整的支付流程
"""

import json
import time
import threading
from datetime import datetime
from urllib.parse import urlparse, parse_qs
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('payment_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class PaymentMonitor:
    def __init__(self):
        self.requests_log = []
        self.payment_requests = []
        self.alipay_requests = []
        self.driver = None
        self.monitoring = False
        
        # 目标URL
        self.target_url = "https://h5.syhy123.com/aqy_msv2/?a=0ba8c5e5f1d60cf6658f6a266a6feb2b&projectid=7526720476899622948&promotionid=7526720211249496127&creativetype=5&phone=13802913949&product=vip_month&uuid=b508528a-5920-4b69-aac4-d89ae25e925c&t=1754893394520&source=generator"
        
        # 监控的域名
        self.target_domains = [
            'api.zzqz2024.com',
            'trace.zzqz2024.com', 
            'h5.syhy123.com',
            'alipay.com',
            'alipays.com'
        ]
        
        # 支付相关关键词
        self.payment_keywords = [
            'signOrder', 'createOrder', 'payment', 'pay', 'order',
            'cashier', 'checkout', 'alipay', '支付'
        ]

    def setup_driver(self):
        """设置Chrome浏览器"""
        chrome_options = Options()
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1')
        
        # 启用网络日志
        chrome_options.add_argument('--enable-logging')
        chrome_options.add_argument('--log-level=0')
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        # 启用性能日志来捕获网络请求
        chrome_options.add_experimental_option('perfLoggingPrefs', {
            'enableNetwork': True,
            'enablePage': False,
        })
        chrome_options.add_experimental_option('loggingPrefs', {
            'performance': 'ALL'
        })
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_cdp_cmd('Network.enable', {})
            logging.info("✅ Chrome浏览器启动成功")
            return True
        except Exception as e:
            logging.error(f"❌ Chrome浏览器启动失败: {e}")
            return False

    def start_monitoring(self):
        """开始监控"""
        if not self.setup_driver():
            return False
            
        self.monitoring = True
        logging.info("🚀 开始监控支付流程...")
        
        try:
            # 访问目标页面
            logging.info(f"📱 访问页面: {self.target_url}")
            self.driver.get(self.target_url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 开始网络监控线程
            monitor_thread = threading.Thread(target=self.monitor_network)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 模拟用户操作
            self.simulate_user_actions()
            
            # 持续监控
            self.continuous_monitoring()
            
        except Exception as e:
            logging.error(f"❌ 监控过程出错: {e}")
        finally:
            self.stop_monitoring()

    def monitor_network(self):
        """监控网络请求"""
        while self.monitoring:
            try:
                # 获取性能日志
                logs = self.driver.get_log('performance')
                
                for log in logs:
                    message = json.loads(log['message'])
                    
                    if message['message']['method'] == 'Network.requestWillBeSent':
                        self.handle_request(message['message']['params'])
                    elif message['message']['method'] == 'Network.responseReceived':
                        self.handle_response(message['message']['params'])
                        
            except Exception as e:
                logging.error(f"网络监控错误: {e}")
                
            time.sleep(0.5)

    def handle_request(self, params):
        """处理网络请求"""
        request = params.get('request', {})
        url = request.get('url', '')
        method = request.get('method', 'GET')
        headers = request.get('headers', {})
        post_data = request.get('postData', '')
        
        timestamp = datetime.now().isoformat()
        
        # 记录所有请求
        request_data = {
            'timestamp': timestamp,
            'type': 'request',
            'method': method,
            'url': url,
            'headers': headers,
            'post_data': post_data,
            'request_id': params.get('requestId')
        }
        
        self.requests_log.append(request_data)
        
        # 检查是否是目标域名
        if any(domain in url for domain in self.target_domains):
            logging.info(f"🌐 [{method}] {url}")
            
            # 检查是否是支付相关请求
            if any(keyword in url.lower() for keyword in self.payment_keywords):
                self.payment_requests.append(request_data)
                logging.warning(f"💰 支付请求: [{method}] {url}")
                
                # 如果有POST数据，记录详细信息
                if post_data:
                    logging.info(f"📤 请求数据: {post_data}")
            
            # 检查是否是支付宝相关
            if 'alipay' in url.lower():
                self.alipay_requests.append(request_data)
                logging.success(f"🚀 支付宝请求: {url}")
                self.analyze_alipay_url(url)

    def handle_response(self, params):
        """处理网络响应"""
        response = params.get('response', {})
        url = response.get('url', '')
        status = response.get('status', 0)
        
        # 只处理目标域名的响应
        if any(domain in url for domain in self.target_domains):
            request_id = params.get('requestId')
            
            try:
                # 获取响应体
                response_body = self.driver.execute_cdp_cmd(
                    'Network.getResponseBody', 
                    {'requestId': request_id}
                )
                
                body = response_body.get('body', '')
                
                # 记录响应
                response_data = {
                    'timestamp': datetime.now().isoformat(),
                    'type': 'response',
                    'url': url,
                    'status': status,
                    'body': body,
                    'request_id': request_id
                }
                
                self.requests_log.append(response_data)
                
                # 如果是支付相关响应，详细记录
                if any(keyword in url.lower() for keyword in self.payment_keywords):
                    logging.info(f"📥 支付响应 [{status}]: {url}")
                    
                    # 尝试解析JSON响应
                    try:
                        if body:
                            json_data = json.loads(body)
                            logging.info(f"📋 响应数据: {json.dumps(json_data, ensure_ascii=False, indent=2)}")
                            
                            # 检查是否包含支付宝链接
                            self.extract_alipay_links(json_data)
                    except json.JSONDecodeError:
                        logging.info(f"📋 响应数据 (非JSON): {body[:500]}...")
                        
            except Exception as e:
                logging.error(f"获取响应体失败: {e}")

    def extract_alipay_links(self, data):
        """从响应数据中提取支付宝链接"""
        def search_dict(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    if isinstance(value, str) and 'alipay' in value.lower():
                        logging.success(f"🔗 发现支付宝链接 ({current_path}): {value}")
                        self.analyze_alipay_url(value)
                    elif isinstance(value, (dict, list)):
                        search_dict(value, current_path)
                        
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    search_dict(item, current_path)
        
        search_dict(data)

    def analyze_alipay_url(self, url):
        """分析支付宝URL"""
        logging.info(f"🔍 分析支付宝链接: {url}")
        
        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)
            
            analysis = {
                'scheme': parsed.scheme,
                'netloc': parsed.netloc,
                'path': parsed.path,
                'params': params,
                'timestamp': datetime.now().isoformat()
            }
            
            logging.info(f"📊 链接分析结果:")
            logging.info(f"   协议: {parsed.scheme}")
            logging.info(f"   域名: {parsed.netloc}")
            logging.info(f"   路径: {parsed.path}")
            
            if params:
                logging.info(f"   参数:")
                for key, values in params.items():
                    logging.info(f"     {key}: {values[0] if values else 'N/A'}")
                    
        except Exception as e:
            logging.error(f"URL分析失败: {e}")

    def simulate_user_actions(self):
        """模拟用户操作"""
        try:
            # 等待页面完全加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            logging.info("📱 页面加载完成，开始模拟用户操作...")
            
            # 查找手机号输入框
            phone_inputs = self.driver.find_elements(By.CSS_SELECTOR, 
                'input[type="tel"], input[name*="phone"], input[placeholder*="手机"], input[id*="phone"]')
            
            if phone_inputs:
                phone_input = phone_inputs[0]
                phone_input.clear()
                phone_input.send_keys("13802913949")
                logging.info("📞 已输入手机号: 13802913949")
                time.sleep(1)
            
            # 查找提交按钮
            submit_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                'button[type="submit"], input[type="submit"], .submit-btn, .pay-btn, .order-btn')
            
            if submit_buttons:
                submit_button = submit_buttons[0]
                self.driver.execute_script("arguments[0].click();", submit_button)
                logging.info("🔘 已点击提交按钮")
                time.sleep(2)
            
            # 查找支付按钮
            pay_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                '.pay-btn, .payment-btn, .alipay-btn, button:contains("支付"), button:contains("确认")')
            
            if pay_buttons:
                pay_button = pay_buttons[0]
                self.driver.execute_script("arguments[0].click();", pay_button)
                logging.info("💰 已点击支付按钮")
                time.sleep(3)
                
        except Exception as e:
            logging.error(f"用户操作模拟失败: {e}")

    def continuous_monitoring(self):
        """持续监控"""
        logging.info("⏰ 开始持续监控，等待支付流程...")
        
        start_time = time.time()
        max_wait_time = 60  # 最大等待60秒
        
        while self.monitoring and (time.time() - start_time) < max_wait_time:
            try:
                # 检查页面URL变化
                current_url = self.driver.current_url
                if 'alipay' in current_url.lower():
                    logging.success(f"🚀 检测到支付宝页面跳转: {current_url}")
                    self.analyze_alipay_url(current_url)
                
                # 检查页面标题变化
                title = self.driver.title
                if '支付宝' in title or 'alipay' in title.lower():
                    logging.success(f"🚀 检测到支付宝页面: {title}")
                
                time.sleep(2)
                
            except Exception as e:
                logging.error(f"持续监控错误: {e}")
                break

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
        if self.driver:
            self.driver.quit()
            
        logging.info("⏹️ 监控已停止")
        
        # 保存监控结果
        self.save_results()

    def save_results(self):
        """保存监控结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        results = {
            'timestamp': timestamp,
            'target_url': self.target_url,
            'total_requests': len(self.requests_log),
            'payment_requests': len(self.payment_requests),
            'alipay_requests': len(self.alipay_requests),
            'all_requests': self.requests_log,
            'payment_requests_detail': self.payment_requests,
            'alipay_requests_detail': self.alipay_requests
        }
        
        filename = f"payment_monitor_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logging.info(f"💾 监控结果已保存到: {filename}")
            
            # 打印统计信息
            logging.info("📊 监控统计:")
            logging.info(f"   总请求数: {len(self.requests_log)}")
            logging.info(f"   支付相关请求: {len(self.payment_requests)}")
            logging.info(f"   支付宝相关请求: {len(self.alipay_requests)}")
            
        except Exception as e:
            logging.error(f"保存结果失败: {e}")

def main():
    """主函数"""
    print("🔍 支付流程接口监控器")
    print("=" * 50)
    
    monitor = PaymentMonitor()
    
    try:
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断监控")
        monitor.stop_monitoring()
    except Exception as e:
        print(f"❌ 监控失败: {e}")
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
