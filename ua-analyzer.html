<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UserAgent分析工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .upload-btn:hover {
            background: #5a6fd8;
        }
        
        .analysis-section {
            display: none;
            margin-top: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .chart-container {
            background: white;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .ua-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        
        .ua-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .ua-item:last-child {
            border-bottom: none;
        }
        
        .ua-text {
            flex: 1;
            font-family: monospace;
            font-size: 12px;
            color: #333;
            margin-right: 15px;
        }
        
        .ua-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .tag-desktop {
            background: #ffebee;
            color: #c62828;
        }
        
        .tag-mobile {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .tag-suspicious {
            background: #fff3e0;
            color: #ef6c00;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .export-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 UserAgent分析工具</h1>
            <p>分析Excel文件中的UserAgent数据，识别电脑和移动设备</p>
        </div>
        
        <div class="content">
            <div class="upload-area" id="uploadArea">
                <div>
                    <h3>📁 上传Excel文件</h3>
                    <p>支持 .xlsx 和 .xls 格式，文件应包含 userAgent 字段</p>
                    <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" />
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        选择文件
                    </button>
                    <p style="margin-top: 15px; color: #666; font-size: 14px;">
                        或拖拽文件到此区域
                    </p>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在分析数据...</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
            
            <div class="analysis-section" id="analysisSection">
                <h2>📈 分析结果</h2>
                
                <div class="stats-grid" id="statsGrid">
                    <!-- 统计卡片将在这里动态生成 -->
                </div>
                
                <div class="chart-container">
                    <h3>设备类型分布</h3>
                    <canvas id="deviceChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>风险等级分布</h3>
                    <canvas id="riskChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>UserAgent样本 (前100条)</h3>
                    <div class="ua-list" id="uaList">
                        <!-- UA列表将在这里动态生成 -->
                    </div>
                </div>
                
                <div class="export-section">
                    <h3>📤 导出结果</h3>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="exportToJSON()">导出JSON</button>
                        <button class="btn btn-success" onclick="exportToCSV()">导出CSV</button>
                        <button class="btn btn-secondary" onclick="exportBlacklist()">导出黑名单</button>
                        <button class="btn btn-danger" onclick="exportSuspicious()">导出可疑UA</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script>
        // 全局变量
        let analysisData = null;
        let deviceChart = null;
        let riskChart = null;

        // UA检测配置（与index.html保持一致）
        const UA_PATTERNS = {
            desktop: [
                'Windows NT', 'Macintosh', 'Intel Mac OS X', 'Linux x86_64',
                'X11', 'WOW64', 'Win64', 'x64', 'AMD64',
                'Chrome/', 'Firefox/', 'Safari/', 'Edge/', 'Opera/',
                'desktop', 'Windows', 'Mac OS', 'Ubuntu', 'Fedora'
            ],
            mobile: [
                'Mobile', 'Android', 'iPhone', 'iPad', 'iPod',
                'BlackBerry', 'Windows Phone', 'Opera Mini',
                'IEMobile', 'Mobile Safari', 'webOS', 'Symbian'
            ],
            suspicious: [
                /HeadlessChrome/i, /PhantomJS/i, /SlimerJS/i,
                /Selenium/i, /WebDriver/i, /Bot/i, /Spider/i, /Crawler/i
            ]
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload();
        });

        // 设置文件上传
        function setupFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            // 文件选择
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFile(e.target.files[0]);
                }
            });
        }

        // 处理文件
        function handleFile(file) {
            if (!file.name.match(/\.(xlsx|xls)$/i)) {
                alert('请选择Excel文件 (.xlsx 或 .xls)');
                return;
            }

            showLoading(true);

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    parseExcelData(e.target.result);
                } catch (error) {
                    console.error('解析文件失败:', error);
                    alert('文件解析失败，请检查文件格式');
                    showLoading(false);
                }
            };
            reader.readAsArrayBuffer(file);
        }

        // 解析Excel数据
        function parseExcelData(data) {
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            console.log('解析到数据行数:', jsonData.length);

            // 查找userAgent字段
            const uaField = findUserAgentField(jsonData[0]);
            if (!uaField) {
                alert('未找到userAgent字段，请确保Excel文件包含用户代理数据');
                showLoading(false);
                return;
            }

            console.log('找到UA字段:', uaField);

            // 提取并分析UA数据
            const userAgents = jsonData
                .map(row => row[uaField])
                .filter(ua => ua && typeof ua === 'string' && ua.trim().length > 0);

            console.log('有效UA数量:', userAgents.length);

            if (userAgents.length === 0) {
                alert('未找到有效的UserAgent数据');
                showLoading(false);
                return;
            }

            // 开始分析
            analyzeUserAgents(userAgents);
        }

        // 查找UserAgent字段
        function findUserAgentField(sampleRow) {
            if (!sampleRow) return null;

            const possibleFields = [
                'userAgent', 'user_agent', 'useragent', 'UserAgent', 'USER_AGENT',
                'ua', 'UA', 'agent', 'Agent', 'browser', 'Browser'
            ];

            for (const field of possibleFields) {
                if (sampleRow.hasOwnProperty(field)) {
                    return field;
                }
            }

            // 如果没有找到标准字段名，查找包含"agent"的字段
            const keys = Object.keys(sampleRow);
            for (const key of keys) {
                if (key.toLowerCase().includes('agent')) {
                    return key;
                }
            }

            return null;
        }

        // 分析UserAgent数据
        function analyzeUserAgents(userAgents) {
            const total = userAgents.length;
            let processed = 0;
            const results = {
                total: total,
                desktop: 0,
                mobile: 0,
                suspicious: 0,
                unknown: 0,
                details: [],
                desktopUAs: [],
                mobileUAs: [],
                suspiciousUAs: []
            };

            // 分批处理，避免阻塞UI
            const batchSize = 100;

            function processBatch(startIndex) {
                const endIndex = Math.min(startIndex + batchSize, total);

                for (let i = startIndex; i < endIndex; i++) {
                    const ua = userAgents[i];
                    const analysis = analyzeUserAgent(ua);

                    results.details.push({
                        userAgent: ua,
                        type: analysis.type,
                        risk: analysis.risk,
                        score: analysis.score,
                        features: analysis.features
                    });

                    // 统计计数
                    results[analysis.type]++;

                    // 收集样本
                    if (analysis.type === 'desktop' && results.desktopUAs.length < 50) {
                        results.desktopUAs.push(ua);
                    } else if (analysis.type === 'mobile' && results.mobileUAs.length < 50) {
                        results.mobileUAs.push(ua);
                    } else if (analysis.type === 'suspicious' && results.suspiciousUAs.length < 50) {
                        results.suspiciousUAs.push(ua);
                    }

                    processed++;
                }

                // 更新进度
                updateProgress(processed / total * 100);

                if (endIndex < total) {
                    // 继续处理下一批
                    setTimeout(() => processBatch(endIndex), 10);
                } else {
                    // 处理完成
                    analysisData = results;
                    showResults(results);
                    showLoading(false);
                }
            }

            // 开始处理
            processBatch(0);
        }

        // 分析单个UserAgent
        function analyzeUserAgent(ua) {
            const lowerUA = ua.toLowerCase();
            let score = 0;
            const features = [];

            // 检测可疑特征
            for (const pattern of UA_PATTERNS.suspicious) {
                if (pattern.test(ua)) {
                    return {
                        type: 'suspicious',
                        risk: 'high',
                        score: -100,
                        features: ['自动化工具特征']
                    };
                }
            }

            // 检测桌面特征
            let desktopMatches = 0;
            for (const keyword of UA_PATTERNS.desktop) {
                if (lowerUA.includes(keyword.toLowerCase())) {
                    desktopMatches++;
                    features.push(`桌面特征: ${keyword}`);
                }
            }

            // 检测移动特征
            let mobileMatches = 0;
            for (const keyword of UA_PATTERNS.mobile) {
                if (lowerUA.includes(keyword.toLowerCase())) {
                    mobileMatches++;
                    features.push(`移动特征: ${keyword}`);
                }
            }

            // 计算得分
            score = mobileMatches * 15 - desktopMatches * 10;

            // 判断类型
            let type, risk;
            if (mobileMatches > 0 && desktopMatches === 0) {
                type = 'mobile';
                risk = 'low';
            } else if (desktopMatches > 0 && mobileMatches === 0) {
                type = 'desktop';
                risk = 'high';
            } else if (mobileMatches > 0 && desktopMatches > 0) {
                type = 'suspicious';
                risk = 'medium';
            } else {
                type = 'unknown';
                risk = 'medium';
            }

            return { type, risk, score, features };
        }

        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('analysisSection').style.display = show ? 'none' : 'block';
        }

        // 更新进度
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        // 显示分析结果
        function showResults(results) {
            // 显示统计卡片
            showStats(results);

            // 显示图表
            showCharts(results);

            // 显示UA列表
            showUAList(results);
        }

        // 显示统计信息
        function showStats(results) {
            const statsGrid = document.getElementById('statsGrid');
            const desktopPercent = (results.desktop / results.total * 100).toFixed(1);
            const mobilePercent = (results.mobile / results.total * 100).toFixed(1);
            const suspiciousPercent = (results.suspicious / results.total * 100).toFixed(1);

            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${results.total.toLocaleString()}</div>
                    <div class="stat-label">总UserAgent数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #dc3545;">${results.desktop.toLocaleString()}</div>
                    <div class="stat-label">桌面设备 (${desktopPercent}%)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #28a745;">${results.mobile.toLocaleString()}</div>
                    <div class="stat-label">移动设备 (${mobilePercent}%)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #ffc107;">${results.suspicious.toLocaleString()}</div>
                    <div class="stat-label">可疑设备 (${suspiciousPercent}%)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #6c757d;">${results.unknown.toLocaleString()}</div>
                    <div class="stat-label">未知设备</div>
                </div>
            `;
        }

        // 显示图表
        function showCharts(results) {
            // 设备类型分布图
            const deviceCtx = document.getElementById('deviceChart').getContext('2d');
            if (deviceChart) deviceChart.destroy();

            deviceChart = new Chart(deviceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['桌面设备', '移动设备', '可疑设备', '未知设备'],
                    datasets: [{
                        data: [results.desktop, results.mobile, results.suspicious, results.unknown],
                        backgroundColor: ['#dc3545', '#28a745', '#ffc107', '#6c757d'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 风险等级分布图
            const riskData = results.details.reduce((acc, item) => {
                acc[item.risk] = (acc[item.risk] || 0) + 1;
                return acc;
            }, {});

            const riskCtx = document.getElementById('riskChart').getContext('2d');
            if (riskChart) riskChart.destroy();

            riskChart = new Chart(riskCtx, {
                type: 'bar',
                data: {
                    labels: ['低风险', '中风险', '高风险'],
                    datasets: [{
                        label: '数量',
                        data: [riskData.low || 0, riskData.medium || 0, riskData.high || 0],
                        backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 显示UA列表
        function showUAList(results) {
            const uaList = document.getElementById('uaList');
            const sampleData = results.details.slice(0, 100); // 只显示前100条

            uaList.innerHTML = sampleData.map(item => {
                let tagClass, tagText;
                switch (item.type) {
                    case 'desktop':
                        tagClass = 'tag-desktop';
                        tagText = '桌面';
                        break;
                    case 'mobile':
                        tagClass = 'tag-mobile';
                        tagText = '移动';
                        break;
                    case 'suspicious':
                        tagClass = 'tag-suspicious';
                        tagText = '可疑';
                        break;
                    default:
                        tagClass = 'tag-suspicious';
                        tagText = '未知';
                }

                return `
                    <div class="ua-item">
                        <div class="ua-text">${item.userAgent}</div>
                        <div class="ua-tag ${tagClass}">${tagText}</div>
                    </div>
                `;
            }).join('');
        }

        // 导出功能
        function exportToJSON() {
            if (!analysisData) return;

            const dataStr = JSON.stringify(analysisData, null, 2);
            downloadFile(dataStr, 'ua-analysis.json', 'application/json');
        }

        function exportToCSV() {
            if (!analysisData) return;

            const headers = ['UserAgent', 'Type', 'Risk', 'Score', 'Features'];
            const csvContent = [
                headers.join(','),
                ...analysisData.details.map(item => [
                    `"${item.userAgent.replace(/"/g, '""')}"`,
                    item.type,
                    item.risk,
                    item.score,
                    `"${item.features.join('; ')}"`
                ].join(','))
            ].join('\n');

            downloadFile(csvContent, 'ua-analysis.csv', 'text/csv');
        }

        function exportBlacklist() {
            if (!analysisData) return;

            const blacklist = analysisData.details
                .filter(item => item.type === 'desktop')
                .map(item => item.userAgent);

            const content = blacklist.join('\n');
            downloadFile(content, 'desktop-ua-blacklist.txt', 'text/plain');
        }

        function exportSuspicious() {
            if (!analysisData) return;

            const suspicious = analysisData.details
                .filter(item => item.type === 'suspicious')
                .map(item => item.userAgent);

            const content = suspicious.join('\n');
            downloadFile(content, 'suspicious-ua-list.txt', 'text/plain');
        }

        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
