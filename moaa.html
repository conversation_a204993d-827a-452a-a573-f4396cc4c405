<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端专用演示页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .device-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 14px;
            color: #495057;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .demo-content {
            margin-top: 30px;
        }
        
        .feature-box {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 移动端演示页面</h1>
        
        <div id="access-status"></div>
        <div id="device-info" class="device-info"></div>
        
        <div id="demo-content" class="demo-content" style="display: none;">
            <div class="feature-box">
                <h3>✅ 访问成功</h3>
                <p>欢迎使用移动端演示页面！</p>
            </div>
            
            <div class="feature-box">
                <h4>🔧 功能演示</h4>
                <p>这里可以放置你的演示内容</p>
                <button onclick="showAlert()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">点击测试</button>
            </div>
            
            <div class="feature-box">
                <h4>📊 设备信息</h4>
                <p>屏幕尺寸: <span id="screen-size"></span></p>
                <p>用户代理: <span id="user-agent"></span></p>
            </div>
        </div>
    </div>

    <script>
        // 禁用右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });

        // 禁用F12和其他开发者工具快捷键
        document.addEventListener('keydown', function(e) {
            // 禁用F12
            if (e.key === 'F12') {
                e.preventDefault();
                return false;
            }
            
            // 禁用Ctrl+Shift+I (开发者工具)
            if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                e.preventDefault();
                return false;
            }
            
            // 禁用Ctrl+Shift+J (控制台)
            if (e.ctrlKey && e.shiftKey && e.key === 'J') {
                e.preventDefault();
                return false;
            }
            
            // 禁用Ctrl+U (查看源代码)
            if (e.ctrlKey && e.key === 'u') {
                e.preventDefault();
                return false;
            }
            
            // 禁用Ctrl+Shift+C (元素选择器)
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                return false;
            }
        });

        // 检测设备类型
        function isMobileDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            const mobileKeywords = [
                'mobile', 'android', 'iphone', 'ipad', 'ipod', 
                'blackberry', 'windows phone', 'opera mini',
                'iemobile', 'mobile safari'
            ];
            
            // 检查用户代理字符串
            const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));
            
            // 检查屏幕尺寸 (移动设备通常宽度小于768px)
            const isMobileScreen = window.innerWidth <= 768;
            
            // 检查触摸支持
            const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            return isMobileUA || (isMobileScreen && hasTouchSupport);
        }

        // 显示设备信息
        function showDeviceInfo() {
            const deviceInfo = document.getElementById('device-info');
            const screenSize = document.getElementById('screen-size');
            const userAgentSpan = document.getElementById('user-agent');
            
            if (screenSize) {
                screenSize.textContent = `${window.innerWidth} x ${window.innerHeight}`;
            }
            
            if (userAgentSpan) {
                userAgentSpan.textContent = navigator.userAgent.substring(0, 50) + '...';
            }
            
            deviceInfo.innerHTML = `
                <strong>设备检测信息:</strong><br>
                屏幕宽度: ${window.innerWidth}px<br>
                触摸支持: ${('ontouchstart' in window) ? '是' : '否'}<br>
                用户代理: ${navigator.userAgent.substring(0, 30)}...
            `;
        }

        // 页面加载完成后执行检测
        window.addEventListener('load', function() {
            const accessStatus = document.getElementById('access-status');
            const demoContent = document.getElementById('demo-content');
            
            if (isMobileDevice()) {
                // 移动设备 - 允许访问
                accessStatus.innerHTML = '<div class="success"><h2>✅ 访问允许</h2><p>检测到移动设备，欢迎访问！</p></div>';
                demoContent.style.display = 'block';
                showDeviceInfo();
            } else {
                // 电脑设备 - 禁止访问
                accessStatus.innerHTML = `
                    <div class="error">
                        <h2>🚫 访问被拒绝</h2>
                        <p><strong>此页面仅限移动设备访问</strong></p>
                        <p>请使用手机或平板电脑访问此演示页面</p>
                        <hr>
                        <small>检测到桌面设备访问</small>
                    </div>
                `;
                
                // 3秒后重定向或关闭
                setTimeout(function() {
                    if (confirm('检测到桌面设备访问，是否关闭页面？')) {
                        window.close();
                    }
                }, 3000);
            }
        });

        // 演示功能
        function showAlert() {
            alert('移动端功能测试成功！');
        }

        // 防止开发者工具检测 (简单方法)
        setInterval(function() {
            if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
                document.body.innerHTML = '<div style="text-align:center; padding:50px; color:red;"><h1>🚫 检测到开发者工具</h1><p>请关闭开发者工具后刷新页面</p></div>';
            }
        }, 1000);

        // 禁用选择文本
        document.addEventListener('selectstart', function(e) {
            e.preventDefault();
            return false;
        });

        // 禁用拖拽
        document.addEventListener('dragstart', function(e) {
            e.preventDefault();
            return false;
        });
    </script>
</body>
</html>
