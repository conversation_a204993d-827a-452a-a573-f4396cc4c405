/**
 * 增强版设备检测器 - 多层防护实现
 * 防止UA伪造和模拟器绕过
 */

class EnhancedDeviceDetector {
    constructor() {
        this.fingerprint = {};
        this.behaviorData = {};
        this.verificationResults = {};
        this.startTime = Date.now();
        
        // 开始收集数据
        this.collectDeviceFingerprint();
        this.startBehaviorTracking();
    }

    // 收集设备指纹
    async collectDeviceFingerprint() {
        this.fingerprint = {
            // 基础信息
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            languages: navigator.languages,
            
            // 屏幕信息
            screen: {
                width: screen.width,
                height: screen.height,
                availWidth: screen.availWidth,
                availHeight: screen.availHeight,
                colorDepth: screen.colorDepth,
                pixelDepth: screen.pixelDepth,
                orientation: screen.orientation ? screen.orientation.angle : 0
            },
            
            // 视口信息
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight,
                outerWidth: window.outerWidth,
                outerHeight: window.outerHeight,
                devicePixelRatio: window.devicePixelRatio
            },
            
            // 触摸能力
            touch: {
                maxTouchPoints: navigator.maxTouchPoints,
                touchSupport: 'ontouchstart' in window,
                touchEvents: this.checkTouchEvents()
            },
            
            // 传感器API
            sensors: await this.checkSensorAPIs(),
            
            // 网络信息
            network: this.getNetworkInfo(),
            
            // 硬件信息
            hardware: await this.getHardwareInfo(),
            
            // 浏览器特性
            browser: this.getBrowserFeatures(),
            
            // 时间戳
            timestamp: Date.now()
        };
    }

    // 检查触摸事件支持
    checkTouchEvents() {
        const touchEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel'];
        return touchEvents.map(event => {
            try {
                return {
                    event: event,
                    supported: event in window || event in document
                };
            } catch (e) {
                return { event: event, supported: false };
            }
        });
    }

    // 检查传感器API
    async checkSensorAPIs() {
        const sensors = {
            deviceMotion: 'DeviceMotionEvent' in window,
            deviceOrientation: 'DeviceOrientationEvent' in window,
            accelerometer: 'Accelerometer' in window,
            gyroscope: 'Gyroscope' in window,
            magnetometer: 'Magnetometer' in window,
            ambientLight: 'AmbientLightSensor' in window,
            proximity: 'ProximitySensor' in window
        };

        // 测试陀螺仪真实性
        if (sensors.deviceOrientation) {
            sensors.gyroscopeReal = await this.testGyroscope();
        }

        // 测试加速度计真实性
        if (sensors.deviceMotion) {
            sensors.accelerometerReal = await this.testAccelerometer();
        }

        return sensors;
    }

    // 测试陀螺仪真实性
    testGyroscope() {
        return new Promise((resolve) => {
            let dataReceived = false;
            let sampleCount = 0;
            const samples = [];

            const handler = (event) => {
                if (event.alpha !== null || event.beta !== null || event.gamma !== null) {
                    dataReceived = true;
                    samples.push({
                        alpha: event.alpha,
                        beta: event.beta,
                        gamma: event.gamma,
                        timestamp: Date.now()
                    });
                    sampleCount++;
                }

                if (sampleCount >= 5) {
                    window.removeEventListener('deviceorientation', handler);
                    resolve({
                        hasData: true,
                        samples: samples,
                        variance: this.calculateVariance(samples)
                    });
                }
            };

            window.addEventListener('deviceorientation', handler);
            
            setTimeout(() => {
                window.removeEventListener('deviceorientation', handler);
                resolve({
                    hasData: dataReceived,
                    samples: samples,
                    variance: samples.length > 1 ? this.calculateVariance(samples) : 0
                });
            }, 2000);
        });
    }

    // 测试加速度计真实性
    testAccelerometer() {
        return new Promise((resolve) => {
            let dataReceived = false;
            let sampleCount = 0;
            const samples = [];

            const handler = (event) => {
                if (event.acceleration && 
                    (event.acceleration.x !== null || 
                     event.acceleration.y !== null || 
                     event.acceleration.z !== null)) {
                    dataReceived = true;
                    samples.push({
                        x: event.acceleration.x,
                        y: event.acceleration.y,
                        z: event.acceleration.z,
                        timestamp: Date.now()
                    });
                    sampleCount++;
                }

                if (sampleCount >= 5) {
                    window.removeEventListener('devicemotion', handler);
                    resolve({
                        hasData: true,
                        samples: samples,
                        variance: this.calculateVariance(samples)
                    });
                }
            };

            window.addEventListener('devicemotion', handler);
            
            setTimeout(() => {
                window.removeEventListener('devicemotion', handler);
                resolve({
                    hasData: dataReceived,
                    samples: samples,
                    variance: samples.length > 1 ? this.calculateVariance(samples) : 0
                });
            }, 2000);
        });
    }

    // 计算方差（用于判断传感器数据真实性）
    calculateVariance(samples) {
        if (samples.length < 2) return 0;
        
        const values = samples.map(s => s.alpha || s.x || 0);
        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
        
        return variance;
    }

    // 获取网络信息
    getNetworkInfo() {
        if (!navigator.connection) return null;
        
        return {
            effectiveType: navigator.connection.effectiveType,
            downlink: navigator.connection.downlink,
            rtt: navigator.connection.rtt,
            saveData: navigator.connection.saveData,
            type: navigator.connection.type
        };
    }

    // 获取硬件信息
    async getHardwareInfo() {
        const hardware = {
            memory: navigator.deviceMemory || null,
            cores: navigator.hardwareConcurrency || null,
            battery: null,
            vibration: 'vibrate' in navigator
        };

        // 电池API
        if ('getBattery' in navigator) {
            try {
                const battery = await navigator.getBattery();
                hardware.battery = {
                    charging: battery.charging,
                    level: battery.level,
                    chargingTime: battery.chargingTime,
                    dischargingTime: battery.dischargingTime
                };
            } catch (e) {
                hardware.battery = null;
            }
        }

        return hardware;
    }

    // 获取浏览器特性
    getBrowserFeatures() {
        return {
            webgl: this.getWebGLInfo(),
            canvas: this.getCanvasFingerprint(),
            audio: this.getAudioFingerprint(),
            fonts: this.getAvailableFonts(),
            plugins: this.getPluginInfo(),
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack,
            webdriver: navigator.webdriver
        };
    }

    // WebGL信息
    getWebGLInfo() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) return null;
            
            return {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                extensions: gl.getSupportedExtensions()
            };
        } catch (e) {
            return null;
        }
    }

    // Canvas指纹
    getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Device fingerprint test 🔒', 2, 2);
            
            return canvas.toDataURL();
        } catch (e) {
            return null;
        }
    }

    // 音频指纹
    getAudioFingerprint() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const analyser = audioContext.createAnalyser();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(analyser);
            analyser.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 1000;
            oscillator.start();
            
            const frequencyData = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(frequencyData);
            
            oscillator.stop();
            audioContext.close();
            
            return Array.from(frequencyData).slice(0, 10).join(',');
        } catch (e) {
            return null;
        }
    }

    // 可用字体检测
    getAvailableFonts() {
        const testFonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New',
            'Verdana', 'Georgia', 'Palatino', 'Garamond',
            'Comic Sans MS', 'Trebuchet MS', 'Arial Black', 'Impact'
        ];
        
        const availableFonts = [];
        const testString = 'mmmmmmmmmmlli';
        const testSize = '72px';
        
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        // 基准字体测量
        context.font = testSize + ' monospace';
        const baselineWidth = context.measureText(testString).width;
        
        testFonts.forEach(font => {
            context.font = testSize + ' ' + font + ', monospace';
            const width = context.measureText(testString).width;
            
            if (width !== baselineWidth) {
                availableFonts.push(font);
            }
        });
        
        return availableFonts;
    }

    // 插件信息
    getPluginInfo() {
        const plugins = [];
        
        for (let i = 0; i < navigator.plugins.length; i++) {
            const plugin = navigator.plugins[i];
            plugins.push({
                name: plugin.name,
                description: plugin.description,
                filename: plugin.filename
            });
        }
        
        return plugins;
    }

    // 开始行为追踪
    startBehaviorTracking() {
        this.behaviorData = {
            mouseEvents: [],
            touchEvents: [],
            scrollEvents: [],
            keyboardEvents: [],
            orientationChanges: [],
            focusEvents: [],
            startTime: Date.now()
        };

        // 鼠标事件
        ['mousedown', 'mouseup', 'mousemove', 'click'].forEach(event => {
            document.addEventListener(event, (e) => {
                this.behaviorData.mouseEvents.push({
                    type: event,
                    x: e.clientX,
                    y: e.clientY,
                    timestamp: Date.now(),
                    button: e.button
                });
            });
        });

        // 触摸事件
        ['touchstart', 'touchmove', 'touchend'].forEach(event => {
            document.addEventListener(event, (e) => {
                const touches = Array.from(e.touches).map(touch => ({
                    x: touch.clientX,
                    y: touch.clientY,
                    force: touch.force || 0,
                    radiusX: touch.radiusX || 0,
                    radiusY: touch.radiusY || 0
                }));
                
                this.behaviorData.touchEvents.push({
                    type: event,
                    touches: touches,
                    timestamp: Date.now()
                });
            });
        });

        // 滚动事件
        window.addEventListener('scroll', (e) => {
            this.behaviorData.scrollEvents.push({
                scrollX: window.scrollX,
                scrollY: window.scrollY,
                timestamp: Date.now()
            });
        });

        // 方向变化
        window.addEventListener('orientationchange', (e) => {
            this.behaviorData.orientationChanges.push({
                orientation: screen.orientation ? screen.orientation.angle : window.orientation,
                timestamp: Date.now()
            });
        });

        // 焦点事件
        ['focus', 'blur', 'visibilitychange'].forEach(event => {
            document.addEventListener(event, (e) => {
                this.behaviorData.focusEvents.push({
                    type: event,
                    hidden: document.hidden,
                    timestamp: Date.now()
                });
            });
        });
    }

    // 综合分析
    async analyze() {
        // 等待数据收集完成
        await new Promise(resolve => setTimeout(resolve, 3000));

        const analysis = {
            fingerprint: this.fingerprint,
            behavior: this.analyzeBehavior(),
            consistency: this.checkConsistency(),
            riskScore: 0,
            riskFactors: [],
            decision: 'allow'
        };

        // 计算风险分数
        analysis.riskScore = this.calculateRiskScore(analysis);
        
        // 确定最终决策
        if (analysis.riskScore >= 80) {
            analysis.decision = 'block';
        } else if (analysis.riskScore >= 50) {
            analysis.decision = 'review';
        } else {
            analysis.decision = 'allow';
        }

        return analysis;
    }

    // 行为分析
    analyzeBehavior() {
        const behavior = {
            hasMouseActivity: this.behaviorData.mouseEvents.length > 0,
            hasTouchActivity: this.behaviorData.touchEvents.length > 0,
            hasScrollActivity: this.behaviorData.scrollEvents.length > 0,
            hasOrientationChange: this.behaviorData.orientationChanges.length > 0,
            inputMethodConsistency: this.checkInputMethodConsistency(),
            touchPressureVariance: this.calculateTouchPressureVariance(),
            scrollPattern: this.analyzeScrollPattern()
        };

        return behavior;
    }

    // 检查输入方法一致性
    checkInputMethodConsistency() {
        const hasMouse = this.behaviorData.mouseEvents.length > 0;
        const hasTouch = this.behaviorData.touchEvents.length > 0;
        
        // 移动设备应该主要使用触摸，很少或没有鼠标事件
        if (hasTouch && !hasMouse) {
            return 'touch_only'; // 符合移动设备
        } else if (hasMouse && !hasTouch) {
            return 'mouse_only'; // 符合桌面设备
        } else if (hasMouse && hasTouch) {
            return 'mixed'; // 可疑
        } else {
            return 'none'; // 没有交互
        }
    }

    // 计算触摸压力方差
    calculateTouchPressureVariance() {
        const pressureValues = this.behaviorData.touchEvents
            .flatMap(event => event.touches)
            .map(touch => touch.force)
            .filter(force => force > 0);

        if (pressureValues.length < 2) return 0;

        const mean = pressureValues.reduce((a, b) => a + b, 0) / pressureValues.length;
        const variance = pressureValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / pressureValues.length;

        return variance;
    }

    // 分析滚动模式
    analyzeScrollPattern() {
        if (this.behaviorData.scrollEvents.length < 2) return null;

        const velocities = [];
        for (let i = 1; i < this.behaviorData.scrollEvents.length; i++) {
            const prev = this.behaviorData.scrollEvents[i - 1];
            const curr = this.behaviorData.scrollEvents[i];
            
            const deltaY = curr.scrollY - prev.scrollY;
            const deltaTime = curr.timestamp - prev.timestamp;
            
            if (deltaTime > 0) {
                velocities.push(Math.abs(deltaY / deltaTime));
            }
        }

        return {
            averageVelocity: velocities.reduce((a, b) => a + b, 0) / velocities.length,
            maxVelocity: Math.max(...velocities),
            velocityVariance: this.calculateVariance(velocities.map((v, i) => ({ value: v, index: i })))
        };
    }

    // 检查一致性
    checkConsistency() {
        const consistency = {
            uaScreenConsistency: this.checkUAScreenConsistency(),
            touchCapabilityConsistency: this.checkTouchCapabilityConsistency(),
            sensorConsistency: this.checkSensorConsistency(),
            networkConsistency: this.checkNetworkConsistency()
        };

        return consistency;
    }

    // 检查UA和屏幕信息一致性
    checkUAScreenConsistency() {
        const ua = this.fingerprint.userAgent.toLowerCase();
        const screen = this.fingerprint.screen;
        
        // 移动设备通常屏幕较小
        const isMobileUA = /mobile|android|iphone|ipad/i.test(ua);
        const isMobileScreen = screen.width <= 768 || screen.height <= 768;
        
        return isMobileUA === isMobileScreen;
    }

    // 检查触摸能力一致性
    checkTouchCapabilityConsistency() {
        const ua = this.fingerprint.userAgent.toLowerCase();
        const touch = this.fingerprint.touch;
        
        const isMobileUA = /mobile|android|iphone|ipad/i.test(ua);
        const hasTouchCapability = touch.touchSupport && touch.maxTouchPoints > 0;
        
        return isMobileUA === hasTouchCapability;
    }

    // 检查传感器一致性
    checkSensorConsistency() {
        const ua = this.fingerprint.userAgent.toLowerCase();
        const sensors = this.fingerprint.sensors;
        
        const isMobileUA = /mobile|android|iphone|ipad/i.test(ua);
        const hasMobileSensors = sensors.deviceMotion || sensors.deviceOrientation;
        
        return isMobileUA === hasMobileSensors;
    }

    // 检查网络一致性
    checkNetworkConsistency() {
        const network = this.fingerprint.network;
        
        if (!network) return true; // 无法检查
        
        // 移动设备更可能有较慢的网络连接
        const isMobileNetwork = network.effectiveType === '3g' || network.effectiveType === '2g';
        const ua = this.fingerprint.userAgent.toLowerCase();
        const isMobileUA = /mobile|android|iphone|ipad/i.test(ua);
        
        // 这个检查不是绝对的，因为移动设备也可能有快速网络
        return true;
    }

    // 计算风险分数
    calculateRiskScore(analysis) {
        let score = 0;
        const factors = [];

        // UA分析
        const ua = analysis.fingerprint.userAgent.toLowerCase();
        if (/windows|macintosh|linux/i.test(ua) && !/mobile/i.test(ua)) {
            score += 30;
            factors.push('桌面操作系统UA');
        }

        // 屏幕尺寸
        const screen = analysis.fingerprint.screen;
        if (screen.width > 1024 || screen.height > 1024) {
            score += 20;
            factors.push('大屏幕尺寸');
        }

        // 触摸支持
        if (!analysis.fingerprint.touch.touchSupport) {
            score += 25;
            factors.push('不支持触摸');
        }

        // 传感器
        if (!analysis.fingerprint.sensors.deviceMotion && !analysis.fingerprint.sensors.deviceOrientation) {
            score += 15;
            factors.push('缺少移动传感器');
        }

        // 行为一致性
        if (analysis.behavior.inputMethodConsistency === 'mouse_only') {
            score += 20;
            factors.push('仅使用鼠标交互');
        } else if (analysis.behavior.inputMethodConsistency === 'mixed') {
            score += 10;
            factors.push('混合输入方式');
        }

        // 一致性检查
        if (!analysis.consistency.uaScreenConsistency) {
            score += 15;
            factors.push('UA与屏幕信息不一致');
        }

        if (!analysis.consistency.touchCapabilityConsistency) {
            score += 15;
            factors.push('UA与触摸能力不一致');
        }

        // WebDriver检测
        if (analysis.fingerprint.browser.webdriver) {
            score += 50;
            factors.push('检测到WebDriver');
        }

        // 传感器数据真实性
        if (analysis.fingerprint.sensors.gyroscopeReal && analysis.fingerprint.sensors.gyroscopeReal.variance === 0) {
            score += 20;
            factors.push('陀螺仪数据异常');
        }

        analysis.riskFactors = factors;
        return Math.min(score, 100); // 最高100分
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedDeviceDetector;
} else {
    window.EnhancedDeviceDetector = EnhancedDeviceDetector;
}
