#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支付流程监控器启动脚本
选择不同的监控方式
"""

import sys
import os
import subprocess
import asyncio

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import requests
        print("✅ requests 已安装")
    except ImportError:
        print("❌ requests 未安装")
        return False
    
    try:
        import selenium
        print("✅ selenium 已安装")
    except ImportError:
        print("❌ selenium 未安装")
        return False
    
    try:
        import playwright
        print("✅ playwright 已安装")
    except ImportError:
        print("❌ playwright 未安装")
        return False
    
    return True

def install_dependencies():
    """安装依赖"""
    print("🔧 正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        
        # 安装playwright浏览器
        subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
        print("✅ Playwright浏览器安装完成")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def run_simple_monitor():
    """运行简化版监控器"""
    print("🚀 启动简化版监控器...")
    try:
        subprocess.run([sys.executable, "simple_monitor.py"])
    except FileNotFoundError:
        print("❌ simple_monitor.py 文件不存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_selenium_monitor():
    """运行Selenium监控器"""
    print("🚀 启动Selenium监控器...")
    try:
        subprocess.run([sys.executable, "payment_monitor.py"])
    except FileNotFoundError:
        print("❌ payment_monitor.py 文件不存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_playwright_monitor():
    """运行Playwright监控器"""
    print("🚀 启动Playwright监控器...")
    try:
        subprocess.run([sys.executable, "playwright_monitor.py"])
    except FileNotFoundError:
        print("❌ playwright_monitor.py 文件不存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("🔍 支付流程监控器")
    print("="*60)
    print("请选择监控方式:")
    print()
    print("1. 简化版监控器 (直接API调用)")
    print("   - 快速测试")
    print("   - 无需浏览器")
    print("   - 轻量级")
    print()
    print("2. Selenium监控器 (完整浏览器自动化)")
    print("   - 完整用户操作模拟")
    print("   - 实时网络监控")
    print("   - 需要Chrome浏览器")
    print()
    print("3. Playwright监控器 (推荐)")
    print("   - 高性能浏览器自动化")
    print("   - 完整网络拦截")
    print("   - 移动设备模拟")
    print()
    print("4. 检查/安装依赖")
    print("5. 退出")
    print("="*60)

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请输入选择 (1-5): ").strip()
            
            if choice == "1":
                if not check_dependencies():
                    print("❌ 依赖未安装，请先选择选项4安装依赖")
                    continue
                run_simple_monitor()
                
            elif choice == "2":
                if not check_dependencies():
                    print("❌ 依赖未安装，请先选择选项4安装依赖")
                    continue
                run_selenium_monitor()
                
            elif choice == "3":
                if not check_dependencies():
                    print("❌ 依赖未安装，请先选择选项4安装依赖")
                    continue
                run_playwright_monitor()
                
            elif choice == "4":
                install_dependencies()
                
            elif choice == "5":
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请输入1-5")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
