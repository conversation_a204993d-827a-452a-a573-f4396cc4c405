# H5下单系统 - 电脑UA拦截解决方案

## 📋 项目概述

这是一个专为H5下单场景设计的电脑UA拦截系统，能够有效识别和阻止桌面设备访问，确保只有移动设备用户能够正常下单。

## 🎯 核心功能

### 1. 前端检测 (`index.html`)
- **多层设备检测算法**
  - UserAgent字符串分析
  - 屏幕尺寸检测
  - 触摸支持检测
  - 设备像素比分析
- **安全防护机制**
  - 禁用F12开发者工具
  - 禁用右键菜单
  - 防止文本选择和拖拽
  - 实时开发者工具检测
- **风险评估系统**
  - 低风险：允许访问
  - 中风险：人工审核
  - 高风险：直接拒绝
  - 极高风险：立即阻止

### 2. 数据分析工具 (`ua-analyzer.html`)
- **Excel文件解析**
  - 支持.xlsx和.xls格式
  - 自动识别UserAgent字段
  - 批量数据处理
- **智能分析**
  - 设备类型识别
  - 风险等级评估
  - 可疑特征检测
- **可视化展示**
  - 设备类型分布图
  - 风险等级统计
  - 详细数据列表
- **数据导出**
  - JSON格式完整数据
  - CSV格式分析结果
  - 桌面UA黑名单
  - 可疑UA列表

### 3. 后端API验证 (`api-validator.js`)
- **设备验证接口**
  - 实时UA分析
  - 多维度风险评估
  - 黑白名单机制
- **订单创建接口**
  - 二次安全验证
  - 风险等级控制
  - 自动拦截机制
- **安全日志系统**
  - 详细访问记录
  - 风险事件追踪
  - 统计数据分析

## 🚀 快速开始

### 前端部署
1. 直接打开 `index.html` 文件
2. 或部署到Web服务器

### 数据分析
1. 打开 `ua-analyzer.html`
2. 上传包含UserAgent数据的Excel文件
3. 查看分析结果并导出数据

### 后端API
```bash
# 安装依赖
npm install

# 启动服务
npm start

# 开发模式
npm run dev
```

## 📊 检测策略

### UserAgent特征识别
```javascript
// 桌面设备特征
desktopKeywords: [
    'Windows NT', 'Macintosh', 'Intel Mac OS X', 'Linux x86_64',
    'X11', 'WOW64', 'Win64', 'x64', 'AMD64',
    'Chrome/', 'Firefox/', 'Safari/', 'Edge/', 'Opera/'
]

// 移动设备特征
mobileKeywords: [
    'Mobile', 'Android', 'iPhone', 'iPad', 'iPod',
    'BlackBerry', 'Windows Phone', 'Opera Mini',
    'IEMobile', 'Mobile Safari', 'webOS', 'Symbian'
]

// 可疑特征
suspiciousPatterns: [
    /HeadlessChrome/i, /PhantomJS/i, /Selenium/i,
    /WebDriver/i, /Bot/i, /Spider/i, /Crawler/i
]
```

### 风险评分算法
- **移动特征**: +15分/个
- **桌面特征**: -10分/个
- **可疑特征**: -50分
- **屏幕尺寸**: ±20分
- **触摸支持**: ±25分

### 决策规则
- **≥15分**: 低风险，允许访问
- **0-14分**: 中风险，人工审核
- **-20-(-1)分**: 高风险，拒绝访问
- **<-20分**: 极高风险，立即阻止

## 🔧 配置说明

### 前端配置
```javascript
const DETECTION_CONFIG = {
    minMobileWidth: 320,
    maxMobileWidth: 768,
    weights: {
        userAgent: 0.4,
        screenSize: 0.3,
        touchSupport: 0.2,
        devicePixelRatio: 0.1
    }
};
```

### 后端配置
```javascript
// 限流配置
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100 // 最多100个请求
});

// 黑名单配置
const blacklist = [
    // 从Excel分析中得到的桌面UA
];
```

## 📈 使用场景

### 1. H5下单系统
- 爱奇艺会员购买
- 视频平台订阅
- 移动端专属商品

### 2. 移动端活动页面
- 限时抢购活动
- 移动端专享优惠
- APP推广页面

### 3. 安全防护
- 防止自动化攻击
- 阻止爬虫访问
- 保护业务数据

## 🛡️ 安全特性

### 前端防护
- 禁用开发者工具
- 防止代码查看
- 实时监控检测
- 页面完整性保护

### 后端验证
- 多层验证机制
- 实时风险评估
- 黑白名单控制
- 详细日志记录

### 数据保护
- 敏感信息加密
- 访问日志审计
- 异常行为检测
- 自动防护响应

## 📝 API文档

### 设备验证
```
POST /api/device/verify
Content-Type: application/json

{
    "deviceInfo": {
        "screenWidth": 375,
        "screenHeight": 812,
        "touchSupport": true,
        "devicePixelRatio": 3
    }
}
```

### 订单创建
```
POST /api/order/create
Content-Type: application/json

{
    "productType": "iqiyi_vip",
    "deviceInfo": {...},
    "securityCheck": {...}
}
```

## 🔍 监控指标

### 关键指标
- 总访问量
- 拦截率
- 误判率
- 响应时间

### 风险指标
- 桌面设备占比
- 可疑访问量
- 黑名单命中率
- 异常行为频率

## 🚨 告警机制

### 自动告警
- 拦截率异常
- 大量可疑访问
- 系统性能异常
- 安全事件触发

### 处理流程
1. 实时监控检测
2. 自动风险评估
3. 分级响应处理
4. 日志记录追踪

## 📞 技术支持

如需技术支持或定制开发，请联系：
- 微信：your_wechat_id
- 邮箱：<EMAIL>

## 📄 许可证

MIT License - 详见 LICENSE 文件
