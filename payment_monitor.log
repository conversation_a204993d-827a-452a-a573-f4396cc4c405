2025-08-11 14:39:35,196 - ERROR - ❌ Chrome浏览器启动失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: loggingPrefs
Stacktrace:
	GetHandleVerifier [0x0x7ff6f146e415+77285]
	GetHandleVerifier [0x0x7ff6f146e470+77376]
	(No symbol) [0x0x7ff6f1239a6a]
	(No symbol) [0x0x7ff6f1268dad]
	(No symbol) [0x0x7ff6f126a6e8]
	(No symbol) [0x0x7ff6f1264b7a]
	(No symbol) [0x0x7ff6f12c41f2]
	(No symbol) [0x0x7ff6f12c3af7]
	(No symbol) [0x0x7ff6f12c5a10]
	(No symbol) [0x0x7ff6f12c57c0]
	(No symbol) [0x0x7ff6f12b83e3]
	(No symbol) [0x0x7ff6f1281521]
	(No symbol) [0x0x7ff6f12822b3]
	GetHandleVerifier [0x0x7ff6f1751efd+3107021]
	GetHandleVerifier [0x0x7ff6f174c29d+3083373]
	GetHandleVerifier [0x0x7ff6f176bedd+3213485]
	GetHandleVerifier [0x0x7ff6f148884e+184862]
	GetHandleVerifier [0x0x7ff6f149055f+216879]
	GetHandleVerifier [0x0x7ff6f1477084+113236]
	GetHandleVerifier [0x0x7ff6f1477239+113673]
	GetHandleVerifier [0x0x7ff6f145e298+11368]
	BaseThreadInitThunk [0x0x7fffe810259d+29]
	RtlUserThreadStart [0x0x7fffe8dcaf78+40]

