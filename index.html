<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5下单系统 - 移动端专用</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .success {
            color: #28a745;
            animation: fadeIn 0.5s ease-in;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            animation: shake 0.5s ease-in-out;
        }
        
        .warning {
            color: #856404;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .device-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            font-size: 14px;
            color: #495057;
            text-align: left;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .order-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid #28a745;
        }
        
        .order-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 120px;
        }
        
        .order-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .order-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .security-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 12px;
            color: #1976d2;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
        
        .risk-level {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin: 5px;
            display: inline-block;
        }
        
        .risk-low { background: #d4edda; color: #155724; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-high { background: #f8d7da; color: #721c24; }
        .risk-critical { background: #f5c6cb; color: #491217; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 H5下单系统</h1>
        
        <div id="loading-screen">
            <div class="loading"></div>
            <p>正在验证设备安全性...</p>
        </div>
        
        <div id="access-status" class="hidden"></div>
        <div id="device-info" class="device-info hidden"></div>
        <div id="security-info" class="security-info hidden"></div>
        
        <div id="order-section" class="order-section hidden">
            <h3>✅ 设备验证通过</h3>
            <p>欢迎使用移动端下单系统</p>
            
            <div style="margin: 20px 0;">
                <button class="order-btn" onclick="createOrder('iqiyi_vip')">
                    📺 爱奇艺VIP会员
                </button>
                <button class="order-btn" onclick="createOrder('iqiyi_sports')">
                    ⚽ 爱奇艺体育会员
                </button>
            </div>
            
            <div id="order-result"></div>
        </div>
    </div>

    <script>
        // 高级设备检测配置
        const DETECTION_CONFIG = {
            // 基于你Excel数据的电脑UA特征
            desktopKeywords: [
                'Windows NT', 'Macintosh', 'Intel Mac OS X', 'Linux x86_64',
                'X11', 'WOW64', 'Win64', 'x64', 'AMD64',
                'Chrome/', 'Firefox/', 'Safari/', 'Edge/', 'Opera/',
                'desktop', 'Windows', 'Mac OS', 'Ubuntu', 'Fedora'
            ],
            
            // 移动设备关键词
            mobileKeywords: [
                'Mobile', 'Android', 'iPhone', 'iPad', 'iPod',
                'BlackBerry', 'Windows Phone', 'Opera Mini',
                'IEMobile', 'Mobile Safari', 'webOS', 'Symbian'
            ],
            
            // 可疑特征
            suspiciousPatterns: [
                /HeadlessChrome/i,
                /PhantomJS/i,
                /SlimerJS/i,
                /Selenium/i,
                /WebDriver/i,
                /Bot/i,
                /Spider/i,
                /Crawler/i
            ],
            
            // 最小移动设备屏幕尺寸
            minMobileWidth: 320,
            maxMobileWidth: 768,
            
            // 检测权重
            weights: {
                userAgent: 0.4,
                screenSize: 0.3,
                touchSupport: 0.2,
                devicePixelRatio: 0.1
            }
        };

        // 设备检测类
        class DeviceDetector {
            constructor() {
                this.userAgent = navigator.userAgent;
                this.screenWidth = window.screen.width;
                this.screenHeight = window.screen.height;
                this.viewportWidth = window.innerWidth;
                this.viewportHeight = window.innerHeight;
                this.devicePixelRatio = window.devicePixelRatio || 1;
                this.touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
                this.orientation = screen.orientation || screen.mozOrientation || screen.msOrientation;
            }

            // 分析用户代理字符串
            analyzeUserAgent() {
                const ua = this.userAgent.toLowerCase();
                let score = 0;
                let details = [];

                // 检测桌面关键词
                const desktopMatches = DETECTION_CONFIG.desktopKeywords.filter(keyword =>
                    ua.includes(keyword.toLowerCase())
                );

                if (desktopMatches.length > 0) {
                    score -= desktopMatches.length * 10;
                    details.push(`检测到桌面特征: ${desktopMatches.join(', ')}`);
                }

                // 检测移动关键词
                const mobileMatches = DETECTION_CONFIG.mobileKeywords.filter(keyword =>
                    ua.includes(keyword.toLowerCase())
                );

                if (mobileMatches.length > 0) {
                    score += mobileMatches.length * 15;
                    details.push(`检测到移动特征: ${mobileMatches.join(', ')}`);
                }

                // 检测可疑模式
                const suspiciousMatches = DETECTION_CONFIG.suspiciousPatterns.filter(pattern =>
                    pattern.test(ua)
                );

                if (suspiciousMatches.length > 0) {
                    score -= 50;
                    details.push(`检测到可疑特征: 自动化工具`);
                }

                return { score, details, userAgent: this.userAgent };
            }

            // 分析屏幕尺寸
            analyzeScreenSize() {
                let score = 0;
                let details = [];

                const width = Math.min(this.screenWidth, this.viewportWidth);
                const height = Math.max(this.screenHeight, this.viewportHeight);

                if (width <= DETECTION_CONFIG.maxMobileWidth) {
                    score += 20;
                    details.push(`移动设备屏幕宽度: ${width}px`);
                } else {
                    score -= 20;
                    details.push(`桌面设备屏幕宽度: ${width}px`);
                }

                // 检测屏幕比例
                const aspectRatio = width / height;
                if (aspectRatio < 1) { // 竖屏更可能是移动设备
                    score += 10;
                    details.push(`竖屏模式 (${aspectRatio.toFixed(2)})`);
                }

                return {
                    score,
                    details,
                    screenSize: `${this.screenWidth}x${this.screenHeight}`,
                    viewportSize: `${this.viewportWidth}x${this.viewportHeight}`
                };
            }

            // 分析触摸支持
            analyzeTouchSupport() {
                let score = 0;
                let details = [];

                if (this.touchSupport) {
                    score += 25;
                    details.push('支持触摸操作');
                } else {
                    score -= 15;
                    details.push('不支持触摸操作');
                }

                // 检测最大触摸点数
                if (navigator.maxTouchPoints > 0) {
                    score += 10;
                    details.push(`最大触摸点数: ${navigator.maxTouchPoints}`);
                }

                return { score, details };
            }

            // 分析设备像素比
            analyzeDevicePixelRatio() {
                let score = 0;
                let details = [];

                if (this.devicePixelRatio > 1) {
                    score += 5; // 高DPI通常是移动设备
                    details.push(`高像素密度: ${this.devicePixelRatio}`);
                } else {
                    details.push(`标准像素密度: ${this.devicePixelRatio}`);
                }

                return { score, details };
            }

            // 综合检测
            detect() {
                const uaResult = this.analyzeUserAgent();
                const screenResult = this.analyzeScreenSize();
                const touchResult = this.analyzeTouchSupport();
                const dprResult = this.analyzeDevicePixelRatio();

                const totalScore =
                    uaResult.score * DETECTION_CONFIG.weights.userAgent +
                    screenResult.score * DETECTION_CONFIG.weights.screenSize +
                    touchResult.score * DETECTION_CONFIG.weights.touchSupport +
                    dprResult.score * DETECTION_CONFIG.weights.devicePixelRatio;

                let riskLevel, riskClass, decision;

                if (totalScore >= 15) {
                    riskLevel = '低风险';
                    riskClass = 'risk-low';
                    decision = 'allow';
                } else if (totalScore >= 0) {
                    riskLevel = '中风险';
                    riskClass = 'risk-medium';
                    decision = 'review';
                } else if (totalScore >= -20) {
                    riskLevel = '高风险';
                    riskClass = 'risk-high';
                    decision = 'block';
                } else {
                    riskLevel = '极高风险';
                    riskClass = 'risk-critical';
                    decision = 'block';
                }

                return {
                    score: totalScore,
                    riskLevel,
                    riskClass,
                    decision,
                    details: {
                        userAgent: uaResult,
                        screen: screenResult,
                        touch: touchResult,
                        devicePixelRatio: dprResult
                    }
                };
            }
        }

        // 安全防护类
        class SecurityProtection {
            constructor() {
                this.startTime = Date.now();
                this.setupProtections();
            }

            setupProtections() {
                // 禁用右键菜单
                document.addEventListener('contextmenu', e => {
                    e.preventDefault();
                    this.logSecurityEvent('右键菜单被禁用');
                    return false;
                });

                // 禁用开发者工具快捷键
                document.addEventListener('keydown', e => {
                    const forbidden = [
                        { key: 'F12' },
                        { key: 'I', ctrl: true, shift: true },
                        { key: 'J', ctrl: true, shift: true },
                        { key: 'U', ctrl: true },
                        { key: 'C', ctrl: true, shift: true },
                        { key: 'S', ctrl: true }
                    ];

                    const current = {
                        key: e.key,
                        ctrl: e.ctrlKey,
                        shift: e.shiftKey,
                        alt: e.altKey
                    };

                    const isForbidden = forbidden.some(f =>
                        f.key === current.key &&
                        (f.ctrl === undefined || f.ctrl === current.ctrl) &&
                        (f.shift === undefined || f.shift === current.shift) &&
                        (f.alt === undefined || f.alt === current.alt)
                    );

                    if (isForbidden) {
                        e.preventDefault();
                        this.logSecurityEvent(`禁用快捷键: ${e.key}`);
                        return false;
                    }
                });

                // 检测开发者工具
                this.detectDevTools();

                // 禁用选择和拖拽
                document.addEventListener('selectstart', e => e.preventDefault());
                document.addEventListener('dragstart', e => e.preventDefault());
            }

            detectDevTools() {
                const threshold = 160;
                setInterval(() => {
                    const widthDiff = window.outerWidth - window.innerWidth;
                    const heightDiff = window.outerHeight - window.innerHeight;

                    if (widthDiff > threshold || heightDiff > threshold) {
                        this.handleDevToolsDetected();
                    }
                }, 1000);
            }

            handleDevToolsDetected() {
                document.body.innerHTML = `
                    <div style="text-align:center; padding:50px; color:red; font-family:Arial;">
                        <h1>🚫 安全警告</h1>
                        <p>检测到开发者工具已打开</p>
                        <p>为保护系统安全，请关闭开发者工具后刷新页面</p>
                        <button onclick="location.reload()" style="padding:10px 20px; margin-top:20px;">刷新页面</button>
                    </div>
                `;
                this.logSecurityEvent('开发者工具被检测到');
            }

            logSecurityEvent(event) {
                console.warn(`[安全事件] ${new Date().toISOString()}: ${event}`);
                // 这里可以发送到后端日志系统
            }
        }

        // 订单处理类
        class OrderProcessor {
            constructor() {
                this.apiBase = '/api'; // 实际API地址
            }

            async createOrder(productType) {
                const detector = new DeviceDetector();
                const detection = detector.detect();

                // 构建订单数据
                const orderData = {
                    productType,
                    deviceInfo: {
                        userAgent: navigator.userAgent,
                        screenSize: `${detector.screenWidth}x${detector.screenHeight}`,
                        viewportSize: `${detector.viewportWidth}x${detector.viewportHeight}`,
                        touchSupport: detector.touchSupport,
                        devicePixelRatio: detector.devicePixelRatio,
                        timestamp: Date.now()
                    },
                    securityCheck: {
                        riskScore: detection.score,
                        riskLevel: detection.riskLevel,
                        decision: detection.decision
                    }
                };

                // 前端验证
                if (detection.decision === 'block') {
                    throw new Error('设备安全验证失败，无法创建订单');
                }

                // 模拟API调用
                return this.simulateOrderAPI(orderData);
            }

            async simulateOrderAPI(orderData) {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

                // 模拟后端验证
                if (orderData.securityCheck.decision === 'block') {
                    throw new Error('后端安全验证失败');
                }

                // 生成模拟订单
                const orderId = 'IQ' + Date.now().toString(36).toUpperCase();
                const paymentUrl = `https://pay.iqiyi.com/order/${orderId}?product=${orderData.productType}`;

                return {
                    success: true,
                    orderId,
                    paymentUrl,
                    amount: orderData.productType === 'iqiyi_vip' ? 19.8 : 39.8,
                    productName: orderData.productType === 'iqiyi_vip' ? '爱奇艺VIP月卡' : '爱奇艺体育月卡'
                };
            }
        }

        // 全局变量
        let detector, security, orderProcessor;

        // 页面加载完成后执行
        window.addEventListener('load', function() {
            // 初始化
            detector = new DeviceDetector();
            security = new SecurityProtection();
            orderProcessor = new OrderProcessor();

            // 延迟检测，增加真实感
            setTimeout(() => {
                performDeviceCheck();
            }, 2000);
        });

        // 执行设备检测
        function performDeviceCheck() {
            const detection = detector.detect();
            const loadingScreen = document.getElementById('loading-screen');
            const accessStatus = document.getElementById('access-status');
            const deviceInfo = document.getElementById('device-info');
            const securityInfo = document.getElementById('security-info');
            const orderSection = document.getElementById('order-section');

            // 隐藏加载屏幕
            loadingScreen.classList.add('hidden');

            // 显示检测结果
            if (detection.decision === 'allow') {
                // 允许访问
                accessStatus.innerHTML = `
                    <div class="success">
                        <h2>✅ 设备验证通过</h2>
                        <p>欢迎使用移动端下单系统</p>
                        <span class="risk-level ${detection.riskClass}">${detection.riskLevel}</span>
                    </div>
                `;
                orderSection.classList.remove('hidden');
            } else if (detection.decision === 'review') {
                // 需要人工审核
                accessStatus.innerHTML = `
                    <div class="warning">
                        <h2>⚠️ 设备需要验证</h2>
                        <p>您的设备存在一些异常特征，请联系客服进行人工验证</p>
                        <span class="risk-level ${detection.riskClass}">${detection.riskLevel}</span>
                        <p style="margin-top:15px;">
                            <button class="order-btn" onclick="contactSupport()">联系客服</button>
                        </p>
                    </div>
                `;
            } else {
                // 拒绝访问
                accessStatus.innerHTML = `
                    <div class="error">
                        <h2>🚫 访问被拒绝</h2>
                        <p><strong>检测到桌面设备访问</strong></p>
                        <p>此下单系统仅限移动设备使用</p>
                        <span class="risk-level ${detection.riskClass}">${detection.riskLevel}</span>
                        <hr style="margin:15px 0;">
                        <small>请使用手机或平板电脑访问</small>
                    </div>
                `;

                // 延迟重定向
                setTimeout(() => {
                    if (confirm('检测到桌面设备访问，是否关闭页面？')) {
                        window.close();
                    }
                }, 5000);
            }

            // 显示设备信息
            deviceInfo.innerHTML = `
                <strong>设备检测详情:</strong><br>
                风险评分: ${detection.score.toFixed(1)}<br>
                屏幕尺寸: ${detection.details.screen.screenSize}<br>
                视口尺寸: ${detection.details.screen.viewportSize}<br>
                触摸支持: ${detection.details.touch.details.join(', ')}<br>
                用户代理: ${detection.details.userAgent.userAgent.substring(0, 50)}...
            `;

            // 显示安全信息
            securityInfo.innerHTML = `
                <strong>🔒 安全保护已启用</strong><br>
                • 禁用开发者工具<br>
                • 禁用右键菜单<br>
                • 实时设备监控<br>
                • 防自动化检测
            `;

            accessStatus.classList.remove('hidden');
            deviceInfo.classList.remove('hidden');
            securityInfo.classList.remove('hidden');
        }

        // 创建订单
        async function createOrder(productType) {
            const resultDiv = document.getElementById('order-result');
            const buttons = document.querySelectorAll('.order-btn');

            // 禁用按钮
            buttons.forEach(btn => btn.disabled = true);

            resultDiv.innerHTML = `
                <div style="padding:20px;">
                    <div class="loading"></div>
                    <p>正在创建订单...</p>
                </div>
            `;

            try {
                const result = await orderProcessor.createOrder(productType);

                resultDiv.innerHTML = `
                    <div class="success" style="padding:20px; border-radius:10px; background:#d4edda;">
                        <h4>✅ 订单创建成功</h4>
                        <p><strong>订单号:</strong> ${result.orderId}</p>
                        <p><strong>商品:</strong> ${result.productName}</p>
                        <p><strong>金额:</strong> ¥${result.amount}</p>
                        <button class="order-btn" onclick="window.open('${result.paymentUrl}', '_blank')" style="margin-top:15px;">
                            前往支付
                        </button>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error" style="padding:20px;">
                        <h4>❌ 订单创建失败</h4>
                        <p>${error.message}</p>
                        <button class="order-btn" onclick="location.reload()" style="margin-top:15px;">
                            重新尝试
                        </button>
                    </div>
                `;
            } finally {
                // 重新启用按钮
                buttons.forEach(btn => btn.disabled = false);
            }
        }

        // 联系客服
        function contactSupport() {
            alert('请联系客服微信: your_wechat_id 进行人工验证');
        }

        // 页面可见性检测
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('页面被隐藏');
            } else {
                console.log('页面重新可见');
                // 重新检测设备（防止切换设备）
                if (detector) {
                    const newDetection = detector.detect();
                    if (newDetection.decision === 'block') {
                        location.reload();
                    }
                }
            }
        });
    </script>
</body>
</html>
