#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版爱奇艺支付宝链接生成器
基于真实链接分析，快速生成支付链接
"""

import hashlib
import time
import random
import string
from urllib.parse import quote, u<PERSON><PERSON><PERSON>

def generate_order_no():
    """生成订单号: YYYYMMDDHHMMSS + 6位随机数"""
    timestamp = time.strftime("%Y%m%d%H%M%S")
    random_suffix = ''.join(random.choices(string.digits, k=6))
    return timestamp + random_suffix

def generate_fv():
    """生成fv参数: 16位十六进制"""
    timestamp = str(int(time.time() * 1000))
    return hashlib.md5(timestamp.encode()).hexdigest()[:16]

def generate_sign(order_no, sku_id, fv, mobile):
    """生成签名: MD5(订单号+SKU+FV+手机号+密钥)"""
    # 模拟签名算法，包含手机号
    sign_data = f"{order_no}{sku_id}{fv}{mobile}iqiyi_secret"
    return hashlib.md5(sign_data.encode()).hexdigest()

def create_alipay_link(sku_id="sku_555029368976707642", mobile="13802913949", custom_order_no=None):
    """创建支付宝链接"""

    # 生成参数
    order_no = custom_order_no or generate_order_no()
    fv = generate_fv()
    sign = generate_sign(order_no, sku_id, fv, mobile)

    # 构建爱奇艺URL参数
    iqiyi_params = {
        'fv': fv,
        'orderNo': order_no,
        'payMethod': 'alipay',
        'sign': sign,
        'skuId': sku_id,
        'mobile': mobile  # 添加手机号参数
    }

    # 爱奇艺完整URL
    iqiyi_url = f"https://vip.iqiyi.com/html5VIP/activity/quickPayWrap/index.html?{urlencode(iqiyi_params)}"

    # 支付宝链接参数
    alipay_params = {
        'appId': '20000067',
        'url': iqiyi_url
    }

    # 最终支付宝链接
    alipay_link = f"alipays://platformapi/startapp?{urlencode(alipay_params)}"

    return {
        'alipay_link': alipay_link,
        'iqiyi_url': iqiyi_url,
        'order_no': order_no,
        'fv': fv,
        'sign': sign,
        'sku_id': sku_id,
        'mobile': mobile
    }

def main():
    """主函数"""
    print("🔗 快速生成爱奇艺支付宝链接")
    print("=" * 50)
    
    # SKU选项
    sku_options = {
        '1': ('sku_555029368976707642', '爱奇艺VIP月卡'),
        '2': ('sku_555029368976707643', '爱奇艺VIP年卡'),
        '3': ('sku_555029368976707644', '爱奇艺体育月卡')
    }
    
    print("产品选择:")
    for key, (sku, name) in sku_options.items():
        print(f"  {key}. {name}")
    
    # 用户选择
    choice = input("\n请选择产品 (1-3): ").strip()
    
    if choice not in sku_options:
        print("❌ 无效选择，使用默认VIP月卡")
        choice = '1'
    
    sku_id, product_name = sku_options[choice]

    # 获取手机号
    mobile = input("\n请输入手机号 (默认: 13802913949): ").strip()
    if not mobile:
        mobile = "13802913949"

    # 验证手机号格式
    if not mobile.isdigit() or len(mobile) != 11:
        print("❌ 手机号格式错误，使用默认手机号")
        mobile = "13802913949"

    # 生成链接
    result = create_alipay_link(sku_id, mobile)

    print(f"\n✅ 生成成功!")
    print("=" * 50)
    print(f"产品: {product_name}")
    print(f"手机号: {result['mobile']}")
    print(f"订单号: {result['order_no']}")
    print(f"签名: {result['sign']}")

    print(f"\n📱 支付宝链接:")
    print(result['alipay_link'])

    # 保存到文件
    filename = f"alipay_link_{result['mobile']}_{result['order_no']}.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"产品: {product_name}\n")
        f.write(f"手机号: {result['mobile']}\n")
        f.write(f"订单号: {result['order_no']}\n")
        f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"\n支付宝链接:\n{result['alipay_link']}\n")
        f.write(f"\n爱奇艺URL:\n{result['iqiyi_url']}\n")

    print(f"\n💾 已保存到: {filename}")

if __name__ == "__main__":
    main()
