# 支付流程监控器使用说明

## 📋 项目概述

这是一个专门用于监控H5支付流程的工具集，可以捕获从下单到支付宝跳转的完整过程，包括所有API调用和网络请求。

## 🎯 监控目标

- **H5页面**: `https://h5.syhy123.com/aqy_msv2/`
- **API接口**: `https://api.zzqz2024.com/api/am/command/encrypt/signOrder`
- **支付宝跳转**: 自动检测和分析支付宝应用调用

## 🛠️ 工具列表

### 1. 前端工具
- `order-generator.html` - 订单链接生成器
- `api-monitor.html` - 浏览器端监控器（受同源策略限制）

### 2. Python监控工具
- `simple_monitor.py` - 简化版API监控器
- `payment_monitor.py` - Selenium完整监控器
- `playwright_monitor.py` - Playwright高性能监控器（推荐）

### 3. 辅助工具
- `run_monitor.py` - 启动脚本
- `ua-analyzer.html` - UserAgent数据分析工具

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

```bash
# 1. 运行启动脚本
python run_monitor.py

# 2. 选择监控方式
# 选项3: Playwright监控器（推荐）
```

### 方法二：手动安装和运行

```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 安装Playwright浏览器（如果使用Playwright）
python -m playwright install chromium

# 3. 运行监控器
python playwright_monitor.py
```

## 📊 监控器对比

| 特性 | 简化版 | Selenium版 | Playwright版 |
|------|--------|------------|--------------|
| 安装难度 | ⭐ | ⭐⭐ | ⭐⭐ |
| 监控完整性 | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 性能 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| 用户操作模拟 | ❌ | ✅ | ✅ |
| 网络请求拦截 | ❌ | ✅ | ✅ |
| 支付宝跳转检测 | ❌ | ✅ | ✅ |
| 移动设备模拟 | ❌ | ✅ | ✅ |

## 🔍 监控功能详解

### 1. 简化版监控器 (`simple_monitor.py`)

**适用场景**: 快速API测试
**功能**:
- 直接调用支付API
- 分析响应中的支付链接
- 无需浏览器环境

**使用方法**:
```bash
python simple_monitor.py
# 输入手机号（默认: 13802913949）
```

### 2. Selenium监控器 (`payment_monitor.py`)

**适用场景**: 完整流程监控
**功能**:
- 自动化Chrome浏览器
- 实时网络请求监控
- 用户操作模拟
- 支付宝跳转检测

**使用方法**:
```bash
python payment_monitor.py
# 自动打开浏览器并开始监控
```

### 3. Playwright监控器 (`playwright_monitor.py`) ⭐推荐

**适用场景**: 高性能专业监控
**功能**:
- 高性能浏览器自动化
- 完整网络请求/响应拦截
- 移动设备UA模拟
- 异步处理，性能优异

**使用方法**:
```bash
python playwright_monitor.py
# 自动启动浏览器并开始监控
```

## 📋 监控输出

### 实时日志
```
🌐 [POST] https://api.zzqz2024.com/api/am/command/encrypt/signOrder
💰 支付请求: [POST] https://api.zzqz2024.com/api/am/command/encrypt/signOrder
📤 请求数据: {"channelCode":"0ba8c5e5f1d60cf6658f6a266a6feb2b",...}
📥 支付响应 [200]: https://api.zzqz2024.com/api/am/command/encrypt/signOrder
🔗 发现支付宝链接: alipays://platformapi/startapp?...
🚀 支付宝请求: alipays://platformapi/startapp?...
```

### JSON数据文件
```json
{
  "timestamp": "20250811_143022",
  "target_url": "https://h5.syhy123.com/aqy_msv2/...",
  "total_requests": 15,
  "payment_requests": 3,
  "alipay_requests": 1,
  "all_requests": [...],
  "payment_requests_detail": [...],
  "alipay_requests_detail": [...]
}
```

## 🔧 配置说明

### 目标URL配置
在监控器中修改 `target_url` 变量：
```python
self.target_url = "你的目标URL"
```

### 监控域名配置
在监控器中修改 `target_domains` 列表：
```python
self.target_domains = [
    'api.zzqz2024.com',
    'trace.zzqz2024.com', 
    'h5.syhy123.com',
    'alipay.com',
    'alipays.com'
]
```

### 支付关键词配置
在监控器中修改 `payment_keywords` 列表：
```python
self.payment_keywords = [
    'signOrder', 'createOrder', 'payment', 'pay', 'order',
    'cashier', 'checkout', 'alipay', '支付'
]
```

## 🐛 常见问题

### 1. Chrome浏览器未找到
```bash
# 安装Chrome浏览器或使用Playwright
python -m playwright install chromium
```

### 2. 依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 重新安装依赖
pip install -r requirements.txt
```

### 3. 网络请求监控不到
- 检查目标域名配置
- 确认页面是否正确加载
- 查看控制台错误信息

### 4. 支付宝链接未检测到
- 确认支付流程是否完整执行
- 检查支付关键词配置
- 查看响应数据是否包含支付链接

## 📊 数据分析

### 使用UA分析工具
1. 打开 `ua-analyzer.html`
2. 上传包含UserAgent数据的Excel文件
3. 查看设备类型分布和风险分析
4. 导出分析结果

### 查看监控日志
1. 运行监控器后会生成JSON文件
2. 使用JSON查看器分析数据
3. 重点关注 `payment_requests_detail` 和 `alipay_requests_detail`

## 🔒 安全注意事项

1. **仅用于测试**: 此工具仅用于技术测试和学习
2. **遵守法律**: 请遵守相关法律法规
3. **数据保护**: 注意保护个人隐私数据
4. **授权使用**: 仅在授权范围内使用

## 📞 技术支持

如遇到问题，请检查：
1. Python版本（建议3.8+）
2. 依赖是否正确安装
3. 网络连接是否正常
4. 目标网站是否可访问

## 📝 更新日志

- v1.0: 初始版本，包含基础监控功能
- v1.1: 添加Playwright支持
- v1.2: 优化网络请求拦截
- v1.3: 添加移动设备模拟
