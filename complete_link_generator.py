#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的爱奇艺支付链接生成器
模拟完整的API调用流程，包括手机号登录和订单生成
"""

import hashlib
import time
import random
import string
import uuid
import json
from urllib.parse import quote, urlencode

class CompleteIqiyiGenerator:
    def __init__(self):
        # API配置
        self.api_base = "https://api.zzqz2024.com"
        self.channel_code = "0ba8c5e5f1d60cf6658f6a266a6feb2b"
        
        # 产品配置
        self.products = {
            'LH2565': {
                'name': '爱奇艺VIP月卡',
                'sku_id': 'sku_555029368976707642',
                'price': 19.8
            },
            'LH2566': {
                'name': '爱奇艺VIP年卡', 
                'sku_id': 'sku_555029368976707643',
                'price': 198
            },
            'LH2567': {
                'name': '爱奇艺体育月卡',
                'sku_id': 'sku_555029368976707644',
                'price': 39.8
            }
        }

    def generate_uuid(self):
        """生成UUID"""
        return str(uuid.uuid4())

    def generate_order_no(self):
        """生成订单号"""
        timestamp = time.strftime("%Y%m%d%H%M%S")
        random_suffix = ''.join(random.choices(string.digits, k=6))
        return timestamp + random_suffix

    def generate_fv(self):
        """生成fv参数"""
        timestamp = str(int(time.time() * 1000))
        return hashlib.md5(timestamp.encode()).hexdigest()[:16]

    def generate_sign(self, order_no, sku_id, fv, mobile):
        """生成签名"""
        sign_data = f"{order_no}{sku_id}{fv}{mobile}iqiyi_secret_key"
        return hashlib.md5(sign_data.encode()).hexdigest()

    def generate_encrypted_data(self, mobile, product_code):
        """生成加密数据 (模拟)"""
        # 实际应该是AES加密，这里用base64模拟
        import base64
        data = {
            'mobile': mobile,
            'productCode': product_code,
            'channelCode': self.channel_code,
            'timestamp': int(time.time() * 1000)
        }
        json_str = json.dumps(data, separators=(',', ':'))
        return base64.b64encode(json_str.encode()).decode()

    def simulate_mobile_login(self, mobile):
        """模拟手机号登录API调用"""
        print(f"📱 模拟手机号登录: {mobile}")
        
        # 模拟登录请求数据
        login_data = {
            "mobile": mobile
        }
        
        # 模拟登录响应
        access_token = f"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.{hashlib.md5(mobile.encode()).hexdigest()}"
        
        login_response = {
            "code": "20000",
            "message": "成功响应",
            "data": {
                "username": mobile,
                "accessToken": access_token,
                "refreshToken": None
            }
        }
        
        print(f"✅ 登录成功，获取Token: {access_token[:50]}...")
        return login_response

    def simulate_sign_order(self, mobile, product_code):
        """模拟签名订单API调用"""
        print(f"💰 模拟订单签名: {mobile} - {product_code}")
        
        # 生成参数
        trace_id = self.generate_uuid()
        encrypted_data = self.generate_encrypted_data(mobile, product_code)
        iv = str(int(time.time() * 1000000))
        t = hashlib.md5(f"{self.channel_code}{iv}".encode()).hexdigest()
        
        # 模拟签名订单请求
        sign_order_data = {
            "channelCode": self.channel_code,
            "data": encrypted_data,
            "iv": iv,
            "t": t,
            "uuid": trace_id,
            "traceId": trace_id
        }
        
        # 模拟响应 - 返回爱奇艺支付链接
        order_no = self.generate_order_no()
        fv = self.generate_fv()
        product_info = self.products[product_code]
        sku_id = product_info['sku_id']
        sign = self.generate_sign(order_no, sku_id, fv, mobile)
        
        # 构建爱奇艺URL
        iqiyi_params = {
            'fv': fv,
            'orderNo': order_no,
            'payMethod': 'alipay',
            'sign': sign,
            'skuId': sku_id,
            'mobile': mobile
        }
        
        iqiyi_url = f"https://vip.iqiyi.com/html5VIP/activity/quickPayWrap/index.html?{urlencode(iqiyi_params)}"
        
        # 构建支付宝链接
        alipay_params = {
            'appId': '20000067',
            'url': iqiyi_url
        }
        
        alipay_link = f"alipays://platformapi/startapp?{urlencode(alipay_params)}"
        
        sign_order_response = {
            "code": "20000",
            "message": "成功响应",
            "data": {
                "encryptData": encrypted_data,
                "alipayLink": alipay_link,
                "iqiyiUrl": iqiyi_url,
                "orderNo": order_no,
                "sign": sign
            }
        }
        
        print(f"✅ 订单签名成功，订单号: {order_no}")
        return sign_order_response

    def simulate_create_order(self, mobile, product_code):
        """模拟创建订单API调用"""
        print(f"📋 模拟创建订单: {mobile} - {product_code}")
        
        trace_id = self.generate_uuid()
        
        # 模拟创建订单请求
        create_order_data = {
            "channelCode": self.channel_code,
            "productCode": product_code,
            "uuid": trace_id,
            "mobile": mobile,
            "traceId": trace_id
        }
        
        # 模拟响应
        order_no = self.generate_order_no()
        
        create_order_response = {
            "code": "20000", 
            "message": "成功响应",
            "data": {
                "orderNo": order_no,
                "status": "PENDING",
                "amount": self.products[product_code]['price']
            }
        }
        
        print(f"✅ 订单创建成功，订单号: {order_no}")
        return create_order_response

    def generate_complete_flow(self, mobile, product_code):
        """生成完整的支付流程"""
        print("🚀 开始完整支付流程生成")
        print("=" * 60)
        
        # 1. 手机号登录
        login_result = self.simulate_mobile_login(mobile)
        
        # 2. 创建订单
        order_result = self.simulate_create_order(mobile, product_code)
        
        # 3. 签名订单
        sign_result = self.simulate_sign_order(mobile, product_code)
        
        # 整合结果
        complete_result = {
            'mobile': mobile,
            'product_code': product_code,
            'product_name': self.products[product_code]['name'],
            'price': self.products[product_code]['price'],
            'login_token': login_result['data']['accessToken'],
            'order_no': sign_result['data']['orderNo'],
            'alipay_link': sign_result['data']['alipayLink'],
            'iqiyi_url': sign_result['data']['iqiyiUrl'],
            'sign': sign_result['data']['sign'],
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        print("\n" + "=" * 60)
        print("✅ 完整流程生成成功!")
        print("=" * 60)
        
        return complete_result

    def save_result(self, result):
        """保存生成结果"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"complete_flow_{result['mobile']}_{timestamp}.json"
        
        # 保存JSON格式
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 保存文本格式
        txt_filename = f"complete_flow_{result['mobile']}_{timestamp}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write("爱奇艺支付链接生成结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {result['timestamp']}\n")
            f.write(f"手机号: {result['mobile']}\n")
            f.write(f"产品: {result['product_name']}\n")
            f.write(f"价格: ¥{result['price']}\n")
            f.write(f"订单号: {result['order_no']}\n")
            f.write(f"签名: {result['sign']}\n")
            f.write(f"\n支付宝链接:\n{result['alipay_link']}\n")
            f.write(f"\n爱奇艺URL:\n{result['iqiyi_url']}\n")
            f.write(f"\n登录Token:\n{result['login_token']}\n")
        
        print(f"💾 结果已保存:")
        print(f"   JSON: {filename}")
        print(f"   TXT:  {txt_filename}")

def main():
    """主函数"""
    generator = CompleteIqiyiGenerator()
    
    print("🔗 完整爱奇艺支付链接生成器")
    print("=" * 60)
    
    # 显示产品选项
    print("可用产品:")
    for code, info in generator.products.items():
        print(f"  {code}: {info['name']} - ¥{info['price']}")
    
    # 用户输入
    print("\n" + "=" * 60)
    mobile = input("请输入手机号: ").strip()
    
    if not mobile or not mobile.isdigit() or len(mobile) != 11:
        print("❌ 手机号格式错误，使用默认: 13802913949")
        mobile = "13802913949"
    
    product_code = input("请输入产品代码 (LH2565/LH2566/LH2567): ").strip().upper()
    
    if product_code not in generator.products:
        print("❌ 产品代码错误，使用默认: LH2565")
        product_code = "LH2565"
    
    # 生成完整流程
    result = generator.generate_complete_flow(mobile, product_code)
    
    # 显示结果
    print(f"\n📱 支付宝链接:")
    print(result['alipay_link'])
    
    # 保存结果
    generator.save_result(result)

if __name__ == "__main__":
    main()
